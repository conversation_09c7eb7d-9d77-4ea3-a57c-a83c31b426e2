#!/bin/bash

# UltraFast Index Demo with Real Network Security Data
# This script demonstrates multi-column queries using mock_data.csv

set -e

echo "🚀 UltraFast Index - Network Security Data Demo"
echo "==============================================="
echo

# Check if mock_data.csv exists
if [ ! -f "mock_data.csv" ]; then
    echo "❌ mock_data.csv not found in current directory"
    echo "Please ensure mock_data.csv is available for this demo"
    exit 1
fi

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21 or later."
    exit 1
fi

echo "✅ Go version: $(go version)"
echo "✅ Found mock_data.csv"

# Analyze the data first
echo "📊 Analyzing mock_data.csv..."
total_lines=$(wc -l < mock_data.csv)
echo "  Total lines: $total_lines (including header)"
echo "  Estimated records: $((total_lines - 1))"

# Show first few lines to understand structure
echo "  Sample data structure:"
head -3 mock_data.csv | cut -d',' -f1-8 | while IFS= read -r line; do
    echo "    $line..."
done
echo

# Build the application
echo "🔧 Building Enhanced UltraFast Index..."
if ! go build -o ultrafast .; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"
echo

# Create output directory
mkdir -p ./mock_indexes

echo "📈 Generating Multi-Column Indexes for Network Security Data..."
echo

# Generate full indexes for the network logs
echo "Generating comprehensive indexes for network security logs..."
if ./ultrafast generate-full mock_data.csv ./mock_indexes network_logs; then
    echo "✅ Network security data indexes generated successfully"
else
    echo "❌ Index generation failed"
    exit 1
fi

echo

# Show generated files
echo "📋 Generated index files:"
ls -lh mock_indexes/ | head -20
echo

# Demonstrate network security queries
echo "🔍 Network Security Analysis Queries"
echo "===================================="
echo

echo "1. Find all TCP traffic that was blocked"
echo "Query: protocol=TCP AND action=Block"
./ultrafast query ./mock_indexes network_logs "protocol=TCP AND action=Block" timestamp,source_ip,destination_ip,rule_name,rule_category
echo

echo "2. Analyze UDP traffic patterns"
echo "Query: protocol=UDP"
./ultrafast query ./mock_indexes network_logs "protocol=UDP" protocol,action,source_country,destination_country,rule_category | head -15
echo

echo "3. Security incidents from specific countries"
echo "Query: source_country=China OR source_country=Russia"
./ultrafast query ./mock_indexes network_logs "source_country=China OR source_country=Russia" source_country,destination_country,protocol,action,rule_category | head -10
echo

echo "4. Web filtering events analysis"
echo "Query: rule_category=\"Web Filtering\""
./ultrafast query ./mock_indexes network_logs "rule_category=\"Web Filtering\"" protocol,action,source_country,destination_country,rule_name | head -10
echo

echo "5. Intrusion detection alerts"
echo "Query: rule_category=\"Intrusion Detection\" AND action=Block"
./ultrafast query ./mock_indexes network_logs "rule_category=\"Intrusion Detection\" AND action=Block" timestamp,source_ip,destination_ip,protocol,rule_name | head -8
echo

echo "6. Complex security analysis: Blocked TCP or allowed ICMP"
echo "Query: (protocol=TCP AND action=Block) OR (protocol=ICMP AND action=Allow)"
./ultrafast query ./mock_indexes network_logs "(protocol=TCP AND action=Block) OR (protocol=ICMP AND action=Allow)" protocol,action,source_country,destination_country,rule_category | head -12
echo

echo "7. Application control events"
echo "Query: rule_category=\"Application Control\""
./ultrafast query ./mock_indexes network_logs "rule_category=\"Application Control\"" protocol,action,source_ip,destination_ip,rule_name | head -8
echo

echo "🎯 Performance Analysis"
echo "======================"
echo

# Create performance test queries for network security scenarios
cat > security_queries.txt << 'EOF'
protocol=TCP
protocol=UDP
protocol=ICMP
action=Block
action=Allow
rule_category="Web Filtering"
rule_category="Intrusion Detection"
rule_category="Application Control"
source_country=China
source_country=Russia
source_country="United States"
destination_country=China
EOF

echo "Running performance benchmark on network security queries..."
./ultrafast batch-search ./mock_indexes security_queries.txt
echo

echo "📊 Advanced Query Performance Testing"
echo "====================================="
echo

# Test complex queries with timing
echo "Testing complex multi-condition queries:"
echo

echo "• Complex AND query (TCP + Web Filtering):"
time ./ultrafast query ./mock_indexes network_logs "protocol=TCP AND rule_category=\"Web Filtering\"" protocol,action,source_country,destination_country | wc -l
echo

echo "• Complex OR query (Multiple protocols):"
time ./ultrafast query ./mock_indexes network_logs "protocol=TCP OR protocol=UDP OR protocol=ICMP" protocol,action | wc -l
echo

echo "• Nested query (Security analysis):"
time ./ultrafast query ./mock_indexes network_logs "(rule_category=\"Intrusion Detection\" AND action=Block) OR (rule_category=\"Web Filtering\" AND source_country=China)" protocol,action,rule_category,source_country | wc -l
echo

echo "🔍 Data Insights from Analysis"
echo "=============================="
echo

# Generate some insights
echo "Protocol distribution analysis:"
./ultrafast query ./mock_indexes network_logs "protocol=TCP" protocol | wc -l | xargs echo "TCP records:"
./ultrafast query ./mock_indexes network_logs "protocol=UDP" protocol | wc -l | xargs echo "UDP records:"
./ultrafast query ./mock_indexes network_logs "protocol=ICMP" protocol | wc -l | xargs echo "ICMP records:"
echo

echo "Action distribution analysis:"
./ultrafast query ./mock_indexes network_logs "action=Allow" action | wc -l | xargs echo "Allowed connections:"
./ultrafast query ./mock_indexes network_logs "action=Block" action | wc -l | xargs echo "Blocked connections:"
echo

echo "Security category analysis:"
./ultrafast query ./mock_indexes network_logs "rule_category=\"Web Filtering\"" rule_category | wc -l | xargs echo "Web Filtering events:"
./ultrafast query ./mock_indexes network_logs "rule_category=\"Intrusion Detection\"" rule_category | wc -l | xargs echo "Intrusion Detection events:"
./ultrafast query ./mock_indexes network_logs "rule_category=\"Application Control\"" rule_category | wc -l | xargs echo "Application Control events:"
echo

echo "🎉 Network Security Analysis Complete!"
echo "======================================"
echo

echo "✅ Successfully demonstrated:"
echo "  • Multi-column indexing of 30K+ network security records"
echo "  • Complex filter expressions with AND/OR logic"
echo "  • Protocol-based traffic analysis"
echo "  • Geographic security pattern analysis"
echo "  • Rule category and action filtering"
echo "  • High-performance query execution"
echo

echo "📈 Performance Highlights:"
echo "  • O(1) average lookup time per column"
echo "  • Efficient bitmap operations for large result sets"
echo "  • Memory-mapped file access for zero-copy operations"
echo "  • Optimized set operations for AND/OR queries"
echo

echo "🎯 Use Cases Demonstrated:"
echo "  • Network security monitoring and analysis"
echo "  • Traffic pattern analysis by protocol"
echo "  • Geographic threat analysis"
echo "  • Security rule effectiveness analysis"
echo "  • Incident response and forensics"
echo

echo "🚀 Next Steps:"
echo "============="
echo
echo "Try these advanced queries:"
echo "  ./ultrafast query ./mock_indexes network_logs \"protocol=TCP AND action=Block AND source_country=China\""
echo "  ./ultrafast query ./mock_indexes network_logs \"(rule_category=\\\"Intrusion Detection\\\" OR rule_category=\\\"Web Filtering\\\") AND action=Block\""
echo "  ./ultrafast query ./mock_indexes network_logs \"protocol=UDP AND (source_country=Russia OR destination_country=Russia)\""
echo

echo "For production deployment:"
echo "  • Scale to millions of records with consistent performance"
echo "  • Implement real-time log ingestion and indexing"
echo "  • Add custom security rule analysis"
echo "  • Integrate with SIEM systems for automated threat detection"
echo

# Cleanup
echo "🧹 Cleaning up demo files..."
# rm -f security_queries.txt
# rm -rf ./mock_indexes
# rm -f ultrafast

echo "✅ Demo complete! Thank you for exploring UltraFast Index."
