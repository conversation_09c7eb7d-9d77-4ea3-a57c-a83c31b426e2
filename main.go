package main

import (
	"encoding/binary"
	"encoding/csv"
	"fmt"
	"os"
	"strings"
	"time"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "generate":
		handleGenerate()
	case "generate-full":
		handleGenerateFull()
	case "generate-v2":
		handleGenerateV2()
	case "search":
		handleSearch()
	case "query":
		handleQuery()
	case "query-v2":
		handleQueryV2()
	case "batch-search":
		handleBatchSearch()
	case "benchmark":
		handleBenchmark()
	case "validate":
		handleValidate()
	case "stats":
		handleStats()
	case "demo":
		fmt.Println("🚀 Running UltraFast Index Demo...")
		fmt.Println("For comprehensive demo, use:")
		fmt.Println("  go run demo_performance_comparison.go")
		fmt.Println("  OR")
		fmt.Println("  ./run_performance_demo.sh")
	case "simple-demo":
		fmt.Println("🚀 Running Simple Demo...")
		fmt.Println("Use: go run demo/simple_demo.go")
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("UltraFast Index - High-Performance Indexing System")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  ultrafast generate <csv_file> <output_dir> <column_name>")
	fmt.Println("  ultrafast generate-full <csv_file> <output_dir> <table_name>")
	fmt.Println("  ultrafast generate-v2 <csv_file> <output_dir> <table_name>")
	fmt.Println("  ultrafast search <index_dir> <column_name> <search_value>")
	fmt.Println("  ultrafast query <index_dir> <table_name> <filter_expression> [select_columns]")
	fmt.Println("  ultrafast query-v2 <index_dir> <table_name> <filter_expression> [select_columns]")
	fmt.Println("  ultrafast batch-search <index_dir> <queries_file>")
	fmt.Println("  ultrafast benchmark <index_dir> <queries_file>")
	fmt.Println("  ultrafast validate <index_file>")
	fmt.Println("  ultrafast stats <index_dir> <column_name>")
	fmt.Println("  ultrafast demo")
	fmt.Println("  ultrafast simple-demo")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  ultrafast generate data.csv ./indexes source_username")
	fmt.Println("  ultrafast generate-full data.csv ./indexes users")
	fmt.Println("  ultrafast generate-v2 data.csv ./indexes users")
	fmt.Println("  ultrafast search ./indexes source_username john_doe")
	fmt.Println("  ultrafast query ./indexes users \"username=john AND status=active\" username,email,status")
	fmt.Println("  ultrafast query-v2 ./indexes users \"username=john AND status=active\" username,email,status")
	fmt.Println("  ultrafast query ./indexes users \"department=IT OR role=admin\"")
	fmt.Println("  ultrafast batch-search ./indexes queries.txt")
	fmt.Println("  ultrafast benchmark ./indexes queries.txt")
	fmt.Println("  ultrafast demo")
	fmt.Println("  ultrafast simple-demo")
	fmt.Println("")
	fmt.Println("Performance Demo:")
	fmt.Println("  go run demo/demo_performance_comparison.go  # Comprehensive comparison")
	fmt.Println("  go run demo/simple_demo.go                  # Quick comparison")
	fmt.Println("  ./run_performance_demo.sh                  # Automated demo script")
}

func handleGenerateV2() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast generate-v2 <csv_file> <output_dir> <table_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	fmt.Printf("Generating UltraFast V2 indexes for table '%s'...\n", tableName)

	// Read entire CSV file
	rows, columns, err := readFullCSV(csvFile)
	if err != nil {
		fmt.Printf("Error reading CSV: %v\n", err)
		return
	}

	fmt.Printf("Read %d rows with %d columns: %v\n", len(rows), len(columns), columns)

	// Create output directory
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return
	}

	start := time.Now()

	// Generate V2 column indexes
	generator := NewV2Generator(outputDir)
	for _, column := range columns {
		columnData := extractColumnData(rows, column)
		if err := generator.GenerateV2(column, columnData); err != nil {
			fmt.Printf("Error generating V2 index for column %s: %v\n", column, err)
			return
		}
		fmt.Printf("Generated V2 index for column: %s\n", column)
	}

	// Generate row store
	rowStore := NewRowStore(outputDir)
	if err := rowStore.GenerateRowStore(tableName, rows); err != nil {
		fmt.Printf("Error generating row store: %v\n", err)
		return
	}

	duration := time.Since(start)
	fmt.Printf("V2 index generation completed in %v\n", duration)

	// Show file info
	fmt.Println("\nGenerated V2 files:")
	for _, column := range columns {
		filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", outputDir, column)
		if stat, err := os.Stat(filename); err == nil {
			fmt.Printf("  %s (%.2f KB)\n", filename, float64(stat.Size())/1024)
		}
	}

	rowStoreFile := fmt.Sprintf("%s/%s.ufrow", outputDir, tableName)
	if stat, err := os.Stat(rowStoreFile); err == nil {
		fmt.Printf("  %s (%.2f KB)\n", rowStoreFile, float64(stat.Size())/1024)
	}
}

func handleQueryV2() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast query-v2 <index_dir> <table_name> <filter_expression> [select_columns]")
		fmt.Println("Examples:")
		fmt.Println("  ultrafast query-v2 ./indexes users \"username=john AND status=active\" username,email")
		fmt.Println("  ultrafast query-v2 ./indexes users \"department=IT OR role=admin\"")
		return
	}

	indexDir := os.Args[2]
	_ = os.Args[3] // tableName - not used in this simple implementation
	filterExpr := os.Args[4]

	var selectCols []string
	if len(os.Args) > 5 {
		selectCols = strings.Split(os.Args[5], ",")
		// Trim whitespace from column names
		for i, col := range selectCols {
			selectCols[i] = strings.TrimSpace(col)
		}
	}

	// Parse filter expression
	filter, err := ParseFilterExpression(filterExpr)
	if err != nil {
		fmt.Printf("Error parsing filter expression: %v\n", err)
		return
	}

	// Execute query using V2 engine
	engine := NewV2QueryEngine(indexDir)

	start := time.Now()

	// For now, use basic search functionality
	// In a full implementation, you'd have a V2-specific query execution
	if filter.Type == FilterTypeLeaf {
		results, err := engine.SearchV2(filter.Column, filter.Value)
		if err != nil {
			fmt.Printf("V2 Query execution error: %v\n", err)
			return
		}

		fmt.Printf("V2 Query executed in %v\n", time.Since(start))
		fmt.Printf("Found %d results\n", len(results))

		if len(results) > 0 {
			fmt.Printf("Line numbers: ")
			for i, lineNum := range results {
				if i > 0 {
					fmt.Printf(", ")
				}
				fmt.Printf("%d", lineNum)
				if i >= 9 { // Show first 10 results
					if len(results) > 10 {
						fmt.Printf(", ... (%d more)", len(results)-10)
					}
					break
				}
			}
			fmt.Println()
		}
	} else {
		fmt.Printf("V2 engine currently supports only simple equality queries\n")
	}
}

func handleGenerate() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast generate <csv_file> <output_dir> <column_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	columnName := os.Args[4]

	fmt.Printf("Generating UltraFast index for column '%s'...\n", columnName)

	// Read CSV file
	data, err := readCSVColumn(csvFile, columnName)
	if err != nil {
		fmt.Printf("Error reading CSV: %v\n", err)
		return
	}

	fmt.Printf("Read %d records\n", len(data))

	// Create output directory
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return
	}

	// Generate index
	generator := NewGenerator(outputDir)
	start := time.Now()

	if err := generator.Generate(columnName, data); err != nil {
		fmt.Printf("Error generating index: %v\n", err)
		return
	}

	duration := time.Since(start)
	fmt.Printf("Index generated successfully in %v\n", duration)

	// Show file info
	filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", outputDir, columnName)
	if stat, err := os.Stat(filename); err == nil {
		fmt.Printf("Index file: %s (%.2f MB)\n", filename, float64(stat.Size())/(1024*1024))
	}
}

func handleGenerateFull() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast generate-full <csv_file> <output_dir> <table_name>")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	fmt.Printf("Generating full UltraFast indexes and row store for table '%s'...\n", tableName)

	// Read entire CSV file
	rows, columns, err := readFullCSV(csvFile)
	if err != nil {
		fmt.Printf("Error reading CSV: %v\n", err)
		return
	}

	fmt.Printf("Read %d rows with %d columns: %v\n", len(rows), len(columns), columns)

	// Create output directory
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		fmt.Printf("Error creating output directory: %v\n", err)
		return
	}

	start := time.Now()

	// Generate column indexes
	generator := NewGenerator(outputDir)
	for _, column := range columns {
		columnData := extractColumnData(rows, column)
		if err := generator.Generate(column, columnData); err != nil {
			fmt.Printf("Error generating index for column %s: %v\n", column, err)
			return
		}
		fmt.Printf("Generated index for column: %s\n", column)
	}

	// Generate row store
	rowStore := NewRowStore(outputDir)
	if err := rowStore.GenerateRowStore(tableName, rows); err != nil {
		fmt.Printf("Error generating row store: %v\n", err)
		return
	}

	duration := time.Since(start)
	fmt.Printf("Full index generation completed in %v\n", duration)

	// Show file info
	fmt.Println("\nGenerated files:")
	for _, column := range columns {
		filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", outputDir, column)
		if stat, err := os.Stat(filename); err == nil {
			fmt.Printf("  %s (%.2f KB)\n", filename, float64(stat.Size())/1024)
		}
	}

	rowStoreFile := fmt.Sprintf("%s/%s.ufrow", outputDir, tableName)
	if stat, err := os.Stat(rowStoreFile); err == nil {
		fmt.Printf("  %s (%.2f KB)\n", rowStoreFile, float64(stat.Size())/1024)
	}
}

func handleSearch() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast search <index_dir> <column_name> <search_value>")
		return
	}

	indexDir := os.Args[2]
	columnName := os.Args[3]
	searchValue := os.Args[4]

	engine := NewQueryEngine(indexDir)
	defer engine.Close()

	start := time.Now()
	results, err := engine.Search(columnName, searchValue)
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("Search error: %v\n", err)
		return
	}

	fmt.Printf("Found %d results in %v\n", len(results), duration)

	if len(results) > 0 {
		fmt.Printf("Line numbers: ")
		for i, lineNum := range results {
			if i > 0 {
				fmt.Printf(", ")
			}
			fmt.Printf("%d", lineNum)
			if i >= 9 { // Show first 10 results
				if len(results) > 10 {
					fmt.Printf(", ... (%d more)", len(results)-10)
				}
				break
			}
		}
		fmt.Println()
	}

	// Show stats
	stats := engine.GetStats()
	fmt.Printf("Performance: %v average lookup time\n", stats.AvgLookupTime)
}

func handleQuery() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast query <index_dir> <table_name> <filter_expression> [select_columns]")
		fmt.Println("Examples:")
		fmt.Println("  ultrafast query ./indexes users \"username=john AND status=active\" username,email")
		fmt.Println("  ultrafast query ./indexes users \"department=IT OR role=admin\"")
		return
	}

	indexDir := os.Args[2]
	tableName := os.Args[3]
	filterExpr := os.Args[4]

	var selectCols []string
	if len(os.Args) > 5 {
		selectCols = strings.Split(os.Args[5], ",")
		// Trim whitespace from column names
		for i, col := range selectCols {
			selectCols[i] = strings.TrimSpace(col)
		}
	}

	// Parse filter expression
	filter, err := ParseFilterExpression(filterExpr)
	if err != nil {
		fmt.Printf("Error parsing filter expression: %v\n", err)
		return
	}

	// Build query request
	query := &QueryRequest{
		Filter:     filter,
		SelectCols: selectCols,
		Limit:      0,
		Offset:     0,
	}

	// Execute query
	engine := NewQueryEngine(indexDir)
	defer engine.Close()

	start := time.Now()
	result, err := engine.ExecuteQuery(tableName, query)
	if err != nil {
		fmt.Printf("Query execution error: %v\n", err)
		return
	}

	fmt.Printf("Query executed in %v\n", result.QueryTime)
	fmt.Printf("Found %d total matches, returning %d rows\n", result.TotalMatches, len(result.Rows))

	// Display results
	if len(result.Rows) > 0 {
		fmt.Println("\nResults:")
		fmt.Println(strings.Repeat("-", 80))

		// Display header
		if len(selectCols) > 0 {
			fmt.Printf("%-10s", "Row")
			for _, col := range selectCols {
				fmt.Printf("%-20s", col)
			}
			fmt.Println()
		} else if len(result.Rows) > 0 {
			// Use columns from first row
			fmt.Printf("%-10s", "Row")
			for col := range result.Rows[0] {
				fmt.Printf("%-20s", col)
			}
			fmt.Println()
		}

		fmt.Println(strings.Repeat("-", 80))

		// Display data (limit to first 20 rows for readability)
		maxRows := len(result.Rows)
		if maxRows > 20 {
			maxRows = 20
		}

		for i := 0; i < maxRows; i++ {
			row := result.Rows[i]
			fmt.Printf("%-10d", i+1)

			if len(selectCols) > 0 {
				for _, col := range selectCols {
					value := row[col]
					if len(value) > 18 {
						value = value[:15] + "..."
					}
					fmt.Printf("%-20s", value)
				}
			} else {
				for _, value := range row {
					if len(value) > 18 {
						value = value[:15] + "..."
					}
					fmt.Printf("%-20s", value)
				}
			}
			fmt.Println()
		}

		if len(result.Rows) > 20 {
			fmt.Printf("... and %d more rows\n", len(result.Rows)-20)
		}
	}

	// Show execution statistics
	fmt.Printf("\nExecution Statistics:\n")
	fmt.Printf("  Indexes used: %v\n", result.Stats.IndexesUsed)
	fmt.Printf("  Index lookups: %d\n", result.Stats.IndexLookups)
	fmt.Printf("  Set operations: %d\n", result.Stats.SetOperations)
	fmt.Printf("  Rows scanned: %d\n", result.Stats.RowsScanned)
	fmt.Printf("  Total execution time: %v\n", time.Since(start))
}

func handleBatchSearch() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: ultrafast batch-search <index_dir> <queries_file>")
		return
	}

	indexDir := os.Args[2]
	queriesFile := os.Args[3]

	// Read queries
	queries, err := readQueries(queriesFile)
	if err != nil {
		fmt.Printf("Error reading queries: %v\n", err)
		return
	}

	engine := NewQueryEngine(indexDir)
	defer engine.Close()

	fmt.Printf("Running %d queries...\n", len(queries))

	totalStart := time.Now()
	var totalResults int

	for i, query := range queries {
		start := time.Now()
		results, err := engine.Search(query.Column, query.Value)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("Query %d error: %v\n", i+1, err)
			continue
		}

		totalResults += len(results)
		fmt.Printf("Query %d: %s=%s -> %d results (%v)\n",
			i+1, query.Column, query.Value, len(results), duration)
	}

	totalDuration := time.Since(totalStart)
	avgDuration := totalDuration / time.Duration(len(queries))

	fmt.Printf("\nBatch complete: %d queries, %d total results in %v\n",
		len(queries), totalResults, totalDuration)
	fmt.Printf("Average query time: %v\n", avgDuration)
}

func handleBenchmark() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: ultrafast benchmark <index_dir> <queries_file>")
		return
	}

	indexDir := os.Args[2]
	queriesFile := os.Args[3]

	queries, err := readQueries(queriesFile)
	if err != nil {
		fmt.Printf("Error reading queries: %v\n", err)
		return
	}

	engine := NewQueryEngine(indexDir)
	defer engine.Close()

	fmt.Printf("=== UltraFast Index Benchmark ===\n")
	fmt.Printf("Queries: %d\n", len(queries))
	fmt.Printf("Index Directory: %s\n\n", indexDir)

	var totalTime time.Duration
	var totalResults int
	var minTime, maxTime time.Duration
	minTime = time.Hour

	fmt.Printf("%-20s %-15s %-12s %-8s\n", "Column", "Search Value", "Duration", "Results")
	fmt.Println(strings.Repeat("-", 60))

	for _, query := range queries {
		start := time.Now()
		results, err := engine.Search(query.Column, query.Value)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("%-20s %-15s %-12s %-8s\n",
				query.Column, query.Value, "ERROR", "0")
			continue
		}

		totalTime += duration
		totalResults += len(results)

		if duration < minTime {
			minTime = duration
		}
		if duration > maxTime {
			maxTime = duration
		}

		fmt.Printf("%-20s %-15s %-12v %-8d\n",
			query.Column, query.Value, duration, len(results))
	}

	avgTime := totalTime / time.Duration(len(queries))

	fmt.Println(strings.Repeat("-", 60))
	fmt.Printf("Total Results: %d\n", totalResults)
	fmt.Printf("Average Time: %v\n", avgTime)
	fmt.Printf("Min Time: %v\n", minTime)
	fmt.Printf("Max Time: %v\n", maxTime)
	fmt.Printf("Total Time: %v\n", totalTime)

	stats := engine.GetStats()
	fmt.Printf("Hash Collisions: %d\n", stats.HashCollisions)
	fmt.Printf("Memory Mapped: %t\n", stats.MemoryMapped)
}

func handleValidate() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: ultrafast validate <index_file>")
		return
	}

	indexFile := os.Args[2]

	// Basic file validation
	file, err := os.Open(indexFile)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		return
	}
	defer file.Close()

	// Read header
	header := make([]byte, 64)
	if _, err := file.Read(header); err != nil {
		fmt.Printf("Error reading header: %v\n", err)
		return
	}

	// Validate magic number
	if string(header[0:8]) != "ULTRAFAS" {
		fmt.Printf("Invalid magic number\n")
		return
	}

	fmt.Printf("✓ Valid UltraFast index file\n")
	fmt.Printf("Magic: %s\n", string(header[0:8]))
	fmt.Printf("Keys: %d\n", binary.BigEndian.Uint32(header[8:12]))
	fmt.Printf("Hash Table Size: %d\n", binary.BigEndian.Uint32(header[24:28]))
	fmt.Printf("Key Slot Size: %d\n", binary.BigEndian.Uint32(header[28:32]))
	fmt.Printf("Version: %d\n", binary.BigEndian.Uint32(header[32:36]))
}

func handleStats() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: ultrafast stats <index_dir> <column_name>")
		return
	}

	indexDir := os.Args[2]
	columnName := os.Args[3]

	filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", indexDir, columnName)

	stat, err := os.Stat(filename)
	if err != nil {
		fmt.Printf("Error accessing file: %v\n", err)
		return
	}

	engine := NewQueryEngine(indexDir)
	defer engine.Close()

	// Trigger mmap by doing a dummy search
	engine.Search(columnName, "dummy")

	stats := engine.GetStats()

	fmt.Printf("=== Index Statistics ===\n")
	fmt.Printf("File: %s\n", filename)
	fmt.Printf("Size: %.2f MB\n", float64(stat.Size())/(1024*1024))
	fmt.Printf("Memory Mapped: %t\n", stats.MemoryMapped)
	fmt.Printf("Last Modified: %v\n", stat.ModTime())
}

// Query represents a search query
type Query struct {
	Column string
	Value  string
}

// readQueries reads queries from a file in format "column=value"
func readQueries(filename string) ([]Query, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var queries []Query

	// Try CSV format first
	reader := csv.NewReader(file)
	reader.Comma = '='

	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	for _, record := range records {
		if len(record) >= 2 {
			queries = append(queries, Query{
				Column: record[0],
				Value:  record[1],
			})
		}
	}

	return queries, nil
}

// readCSVColumn reads a specific column from a CSV file
func readCSVColumn(filename, columnName string) ([]Record, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("empty CSV file")
	}

	// Find column index
	header := records[0]
	columnIndex := -1
	for i, col := range header {
		if col == columnName {
			columnIndex = i
			break
		}
	}

	if columnIndex == -1 {
		return nil, fmt.Errorf("column '%s' not found", columnName)
	}

	// Extract data
	var data []Record
	for lineNum, row := range records[1:] { // Skip header
		if columnIndex < len(row) && row[columnIndex] != "" {
			data = append(data, Record{
				LineNumber: uint32(lineNum + 2), // +2 because we skip header and are 1-based
				Value:      row[columnIndex],
			})
		}
	}

	return data, nil
}

// readFullCSV reads the entire CSV file and returns all rows with column metadata
func readFullCSV(filename string) ([]RowRecord, []string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, nil, err
	}

	if len(records) == 0 {
		return nil, nil, fmt.Errorf("empty CSV file")
	}

	// Extract column names from header
	columns := records[0]

	// Convert records to RowRecord format
	var rows []RowRecord
	for lineNum, record := range records[1:] { // Skip header
		if len(record) == 0 {
			continue // Skip empty rows
		}

		values := make(map[string]string)
		for i, value := range record {
			if i < len(columns) {
				values[columns[i]] = value
			}
		}

		rows = append(rows, RowRecord{
			LineNumber: uint32(lineNum + 2), // +2 because we skip header and are 1-based
			Values:     values,
		})
	}

	return rows, columns, nil
}

// extractColumnData extracts data for a specific column from rows
func extractColumnData(rows []RowRecord, columnName string) []Record {
	var data []Record

	for _, row := range rows {
		if value, exists := row.Values[columnName]; exists && value != "" {
			data = append(data, Record{
				LineNumber: row.LineNumber,
				Value:      value,
			})
		}
	}

	return data
}
