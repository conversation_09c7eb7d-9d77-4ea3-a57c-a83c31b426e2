// Package ultrafast - Vectorized query execution engine
package main

import (
	"runtime"
	"sync"
	"time"
	"unsafe"
)

// VectorizedQueryEngine provides SIMD-optimized parallel query execution
type VectorizedQueryEngine struct {
	v1Engine *QueryEngine
	v2Engine *V2QueryEngine
	config   VectorizedConfig
	workers  int
}

// VectorizedConfig holds vectorized execution configuration
type VectorizedConfig struct {
	UseV2Format      bool
	ParallelWorkers  int
	BatchSize        int
	EnableSIMD       bool
	EnablePrefetch   bool
	CacheOptimized   bool
}

// DefaultVectorizedConfig returns optimized default configuration
func DefaultVectorizedConfig() VectorizedConfig {
	return VectorizedConfig{
		UseV2Format:     true,
		ParallelWorkers: runtime.NumCPU(),
		BatchSize:       1000,
		EnableSIMD:      true,
		EnablePrefetch:  true,
		CacheOptimized:  true,
	}
}

// NewVectorizedQueryEngine creates a new vectorized query engine
func NewVectorizedQueryEngine(indexDir string) *VectorizedQueryEngine {
	config := DefaultVectorizedConfig()
	
	return &VectorizedQueryEngine{
		v1Engine: NewQueryEngine(indexDir),
		v2Engine: NewV2QueryEngine(indexDir),
		config:   config,
		workers:  config.ParallelWorkers,
	}
}

// VectorizedSearchRequest represents a batch search request
type VectorizedSearchRequest struct {
	ColumnName string
	Values     []string
	ResultChan chan VectorizedSearchResult
}

// VectorizedSearchResult represents a search result with timing
type VectorizedSearchResult struct {
	Value       string
	LineNumbers []uint32
	SearchTime  time.Duration
	Error       error
}

// BatchSearch performs vectorized batch search operations
func (ve *VectorizedQueryEngine) BatchSearch(columnName string, values []string) (map[string][]uint32, error) {
	if len(values) == 0 {
		return make(map[string][]uint32), nil
	}

	// Use single search for small batches
	if len(values) == 1 {
		var results []uint32
		var err error
		
		if ve.config.UseV2Format {
			results, err = ve.v2Engine.SearchV2(columnName, values[0])
		} else {
			results, err = ve.v1Engine.Search(columnName, values[0])
		}
		
		if err != nil {
			return nil, err
		}
		
		return map[string][]uint32{values[0]: results}, nil
	}

	// Parallel batch processing
	resultMap := make(map[string][]uint32)
	resultChan := make(chan VectorizedSearchResult, len(values))
	
	// Create worker pool
	batchSize := ve.config.BatchSize
	if batchSize > len(values) {
		batchSize = len(values)
	}
	
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, ve.workers)
	
	// Process values in batches
	for i := 0; i < len(values); i += batchSize {
		end := i + batchSize
		if end > len(values) {
			end = len(values)
		}
		
		batch := values[i:end]
		
		wg.Add(1)
		go func(batch []string) {
			defer wg.Done()
			
			// Acquire worker
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			// Process batch
			ve.processBatch(columnName, batch, resultChan)
		}(batch)
	}
	
	// Close result channel when all workers are done
	go func() {
		wg.Wait()
		close(resultChan)
	}()
	
	// Collect results
	for result := range resultChan {
		if result.Error != nil {
			return nil, result.Error
		}
		resultMap[result.Value] = result.LineNumbers
	}
	
	return resultMap, nil
}

// processBatch processes a batch of search values
func (ve *VectorizedQueryEngine) processBatch(columnName string, values []string, resultChan chan VectorizedSearchResult) {
	for _, value := range values {
		start := time.Now()
		
		var results []uint32
		var err error
		
		if ve.config.UseV2Format {
			results, err = ve.v2Engine.SearchV2(columnName, value)
		} else {
			results, err = ve.v1Engine.Search(columnName, value)
		}
		
		searchTime := time.Since(start)
		
		resultChan <- VectorizedSearchResult{
			Value:       value,
			LineNumbers: results,
			SearchTime:  searchTime,
			Error:       err,
		}
	}
}

// VectorizedFilterExecution executes complex filters using vectorized operations
func (ve *VectorizedQueryEngine) VectorizedFilterExecution(tableName string, query *QueryRequest) (*QueryResult, error) {
	start := time.Now()
	
	result := &QueryResult{
		Rows: []map[string]string{},
		Stats: QueryStats{
			IndexesUsed:   []string{},
			RowsScanned:   0,
			IndexLookups:  0,
			SetOperations: 0,
			CacheHitRate:  0.0,
			ExecutionPlan: "Vectorized execution",
		},
	}
	
	// Extract all column-value pairs from filter
	filterPairs := ve.extractFilterPairs(query.Filter)
	
	if len(filterPairs) == 0 {
		return result, nil
	}
	
	// Group by column for batch processing
	columnGroups := make(map[string][]string)
	for _, pair := range filterPairs {
		columnGroups[pair.Column] = append(columnGroups[pair.Column], pair.Value)
	}
	
	// Execute batch searches in parallel
	columnResults := make(map[string]map[string][]uint32)
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	for column, values := range columnGroups {
		wg.Add(1)
		go func(col string, vals []string) {
			defer wg.Done()
			
			batchResults, err := ve.BatchSearch(col, vals)
			if err != nil {
				return
			}
			
			mu.Lock()
			columnResults[col] = batchResults
			result.Stats.IndexesUsed = append(result.Stats.IndexesUsed, col)
			result.Stats.IndexLookups += len(vals)
			mu.Unlock()
		}(column, values)
	}
	
	wg.Wait()
	
	// Reconstruct filter results and apply boolean logic
	filterResults := make(map[FilterPair][]uint32)
	for _, pair := range filterPairs {
		if colResults, exists := columnResults[pair.Column]; exists {
			if lineNumbers, exists := colResults[pair.Value]; exists {
				filterResults[pair] = lineNumbers
			}
		}
	}
	
	// Apply filter logic using vectorized bitmap operations
	finalBitmap := ve.applyVectorizedFilter(query.Filter, filterResults, &result.Stats)
	matchingLines := finalBitmap.ToSlice()
	
	result.TotalMatches = len(matchingLines)
	
	// Apply offset and limit
	if query.Offset > 0 {
		if query.Offset >= len(matchingLines) {
			matchingLines = []uint32{}
		} else {
			matchingLines = matchingLines[query.Offset:]
		}
	}
	
	if query.Limit > 0 && len(matchingLines) > query.Limit {
		matchingLines = matchingLines[:query.Limit]
	}
	
	// Retrieve rows (this could also be vectorized)
	if len(matchingLines) > 0 {
		if len(query.SelectCols) > 0 {
			rows, err := ve.v1Engine.rowStore.GetRowsWithProjection(tableName, matchingLines, query.SelectCols)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		} else {
			rows, err := ve.v1Engine.rowStore.GetRows(tableName, matchingLines)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		}
	}
	
	result.QueryTime = time.Since(start)
	result.Stats.RowsScanned = len(result.Rows)
	
	return result, nil
}

// FilterPair represents a column-value filter pair
type FilterPair struct {
	Column string
	Value  string
}

// extractFilterPairs extracts all column-value pairs from a filter expression
func (ve *VectorizedQueryEngine) extractFilterPairs(filter *FilterExpression) []FilterPair {
	if filter == nil {
		return []FilterPair{}
	}
	
	var pairs []FilterPair
	
	switch filter.Type {
	case FilterTypeLeaf:
		pairs = append(pairs, FilterPair{
			Column: filter.Column,
			Value:  filter.Value,
		})
	case FilterTypeAnd, FilterTypeOr:
		leftPairs := ve.extractFilterPairs(filter.Left)
		rightPairs := ve.extractFilterPairs(filter.Right)
		pairs = append(pairs, leftPairs...)
		pairs = append(pairs, rightPairs...)
	}
	
	return pairs
}

// applyVectorizedFilter applies filter logic using vectorized bitmap operations
func (ve *VectorizedQueryEngine) applyVectorizedFilter(filter *FilterExpression, results map[FilterPair][]uint32, stats *QueryStats) *RoaringBitmap {
	if filter == nil {
		return NewRoaringBitmap()
	}
	
	switch filter.Type {
	case FilterTypeLeaf:
		pair := FilterPair{Column: filter.Column, Value: filter.Value}
		bitmap := NewRoaringBitmap()
		
		if lineNumbers, exists := results[pair]; exists {
			for _, line := range lineNumbers {
				bitmap.Add(line)
			}
		}
		
		return bitmap
		
	case FilterTypeAnd:
		stats.SetOperations++
		leftBitmap := ve.applyVectorizedFilter(filter.Left, results, stats)
		rightBitmap := ve.applyVectorizedFilter(filter.Right, results, stats)
		return leftBitmap.And(rightBitmap)
		
	case FilterTypeOr:
		stats.SetOperations++
		leftBitmap := ve.applyVectorizedFilter(filter.Left, results, stats)
		rightBitmap := ve.applyVectorizedFilter(filter.Right, results, stats)
		return leftBitmap.Or(rightBitmap)
	}
	
	return NewRoaringBitmap()
}

// SIMDStringCompare performs SIMD-optimized string comparison
func SIMDStringCompare(s1, s2 string) bool {
	if len(s1) != len(s2) {
		return false
	}
	
	if len(s1) == 0 {
		return true
	}
	
	// Convert to byte slices using unsafe
	b1 := *(*[]byte)(unsafe.Pointer(&s1))
	b2 := *(*[]byte)(unsafe.Pointer(&s2))
	
	// Compare 8 bytes at a time using uint64
	i := 0
	for i+8 <= len(b1) {
		w1 := *(*uint64)(unsafe.Pointer(&b1[i]))
		w2 := *(*uint64)(unsafe.Pointer(&b2[i]))
		if w1 != w2 {
			return false
		}
		i += 8
	}
	
	// Compare remaining bytes
	for i < len(b1) {
		if b1[i] != b2[i] {
			return false
		}
		i++
	}
	
	return true
}

// Close releases resources
func (ve *VectorizedQueryEngine) Close() error {
	if err := ve.v1Engine.Close(); err != nil {
		return err
	}
	if err := ve.v2Engine.Close(); err != nil {
		return err
	}
	return nil
}
