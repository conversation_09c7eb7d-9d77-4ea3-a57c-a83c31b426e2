# Three-Way Performance Comparison Demo

## Quick Start

```bash
# Make script executable
chmod +x run_demo.sh

# Run the demo
./run_demo.sh
```

Then choose option **3** for the three-way comparison.

## What Gets Compared

### 1. **Existing Implementation**
- Standard binary format indexes
- Baseline performance reference

### 2. **ExistingInx Implementation** 
- Compressed binary format indexes
- Improved storage efficiency
- Uses `generate-compressed` command

### 3. **Ultrafast (V2) Implementation**
- Advanced V2 format with optimizations
- Bloom filters, compression, SIMD optimizations
- Uses `generate-v2` and `query-v2` commands

## Performance Metrics

The demo measures and compares:

- **Index Generation Time** - How long it takes to build indexes
- **Storage Size** - Disk space used by indexes  
- **Query Performance** - Average query time and queries per second
- **Speedup Ratios** - Performance improvement vs baseline

## Expected Results

Based on the user's memory preferences, you should see:

```
Implementation | Gen Time | Storage | Avg Query | QPS    | Speedup
---------------|----------|---------|-----------|--------|--------
Existing       | ~5.4s    | ~XX MB  | ~XXXµs    | ~XXXX  | 1.0x
ExistingInx    | ~X.Xs    | ~XX MB  | ~XXXµs    | ~XXXX  | X.Xx
Ultrafast      | ~X.Xs    | ~XX MB  | ~XXµs     | ~XXXXX | XX.x
```

The Ultrafast implementation should demonstrate microsecond-level query performance comparable to enterprise engines like ClickHouse and BigQuery.

## Test Queries

The demo uses these filter conditions on mock_data.csv:

1. `protocol=TCP` (High selectivity)
2. `source_username=odjordjevicrp` (Medium selectivity) 
3. `rule_name=ALLOW_HTTP` (Low selectivity)
4. `destination_country=United States` (Geographic)
5. `action=ALLOW` (Action-based)

## Alternative Demo Options

- **Option 1**: Full 4-way comparison (includes UltraFast V1)
- **Option 2**: Quick 2-way comparison (Existing vs UltraFast V1)
- **Option 4**: Show available commands and examples
