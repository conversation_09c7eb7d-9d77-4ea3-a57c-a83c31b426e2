package main

import (
	"os"
	"testing"
	"time"
)

func TestV2FormatPerformance(t *testing.T) {
	// Create temporary directory
	tempDir := "/tmp/ultrafast_v2_test"
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Generate test data
	records := []Record{
		{LineNumber: 1, Value: "TCP"},
		{LineNumber: 2, Value: "UDP"},
		{LineNumber: 3, Value: "TCP"},
		{LineNumber: 4, Value: "ICMP"},
		{LineNumber: 5, Value: "TCP"},
		{LineNumber: 6, Value: "UDP"},
		{LineNumber: 7, Value: "TCP"},
		{LineNumber: 8, Value: "ICMP"},
	}

	// Test V2 generator
	generator := NewV2Generator(tempDir)
	start := time.Now()
	err := generator.GenerateV2("protocol", records)
	generateTime := time.Since(start)

	if err != nil {
		t.Fatalf("Failed to generate V2 index: %v", err)
	}

	t.Logf("V2 index generation time: %v", generateTime)

	// Test V2 query engine
	engine := NewV2QueryEngine(tempDir)
	defer engine.Close()

	// Test search performance
	start = time.Now()
	results, err := engine.SearchV2("protocol", "TCP")
	searchTime := time.Since(start)

	if err != nil {
		t.Fatalf("V2 search failed: %v", err)
	}

	t.Logf("V2 search time: %v", searchTime)
	t.Logf("V2 results: %v", results)

	// Verify results
	expectedTCP := []uint32{1, 3, 5, 7}
	if len(results) != len(expectedTCP) {
		t.Errorf("Expected %d TCP results, got %d", len(expectedTCP), len(results))
	}

	for i, expected := range expectedTCP {
		if i >= len(results) || results[i] != expected {
			t.Errorf("Expected result[%d] = %d, got %d", i, expected, results[i])
		}
	}

	// Test non-existent value
	start = time.Now()
	results, err = engine.SearchV2("protocol", "HTTP")
	searchTime = time.Since(start)

	if err != nil {
		t.Fatalf("V2 search for non-existent value failed: %v", err)
	}

	t.Logf("V2 negative search time: %v", searchTime)

	if len(results) != 0 {
		t.Errorf("Expected 0 results for HTTP, got %d", len(results))
	}
}

func BenchmarkV2vsV1Performance(b *testing.B) {
	// Create temporary directories
	tempDirV1 := "/tmp/ultrafast_v1_bench"
	tempDirV2 := "/tmp/ultrafast_v2_bench"
	os.MkdirAll(tempDirV1, 0755)
	os.MkdirAll(tempDirV2, 0755)
	defer os.RemoveAll(tempDirV1)
	defer os.RemoveAll(tempDirV2)

	// Generate larger test dataset
	records := make([]Record, 10000)
	protocols := []string{"TCP", "UDP", "ICMP", "HTTP", "HTTPS", "FTP", "SSH", "DNS"}
	
	for i := 0; i < 10000; i++ {
		records[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      protocols[i%len(protocols)],
		}
	}

	// Generate V1 index
	generatorV1 := NewGenerator(tempDirV1)
	err := generatorV1.Generate("protocol", records)
	if err != nil {
		b.Fatalf("Failed to generate V1 index: %v", err)
	}

	// Generate V2 index
	generatorV2 := NewV2Generator(tempDirV2)
	err = generatorV2.GenerateV2("protocol", records)
	if err != nil {
		b.Fatalf("Failed to generate V2 index: %v", err)
	}

	// Benchmark V1 search
	engineV1 := NewQueryEngine(tempDirV1)
	defer engineV1.Close()

	b.Run("V1_Search", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := engineV1.Search("protocol", "TCP")
			if err != nil {
				b.Fatalf("V1 search failed: %v", err)
			}
		}
	})

	// Benchmark V2 search
	engineV2 := NewV2QueryEngine(tempDirV2)
	defer engineV2.Close()

	b.Run("V2_Search", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := engineV2.SearchV2("protocol", "TCP")
			if err != nil {
				b.Fatalf("V2 search failed: %v", err)
			}
		}
	})

	// Benchmark negative lookups (bloom filter advantage)
	b.Run("V1_NegativeSearch", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := engineV1.Search("protocol", "NONEXISTENT")
			if err != nil {
				b.Fatalf("V1 negative search failed: %v", err)
			}
		}
	})

	b.Run("V2_NegativeSearch", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := engineV2.SearchV2("protocol", "NONEXISTENT")
			if err != nil {
				b.Fatalf("V2 negative search failed: %v", err)
			}
		}
	})
}

func TestBloomFilterAccuracy(t *testing.T) {
	// Test bloom filter false positive rate
	bf := NewBloomFilter(1000, 0.01) // 1% false positive rate

	// Add known elements
	testElements := []string{"TCP", "UDP", "ICMP", "HTTP", "HTTPS", "FTP", "SSH", "DNS"}
	for _, elem := range testElements {
		bf.Add(elem)
	}

	// Test known elements (should all return true)
	for _, elem := range testElements {
		if !bf.Contains(elem) {
			t.Errorf("Bloom filter should contain %s", elem)
		}
	}

	// Test unknown elements (may have false positives)
	unknownElements := []string{"SMTP", "POP3", "IMAP", "SNMP", "TELNET"}
	falsePositives := 0
	
	for _, elem := range unknownElements {
		if bf.Contains(elem) {
			falsePositives++
		}
	}

	falsePositiveRate := float64(falsePositives) / float64(len(unknownElements))
	t.Logf("False positive rate: %.2f%% (%d/%d)", falsePositiveRate*100, falsePositives, len(unknownElements))

	// Should be reasonably close to expected rate (1%)
	if falsePositiveRate > 0.05 { // Allow up to 5% due to small sample size
		t.Errorf("False positive rate too high: %.2f%%", falsePositiveRate*100)
	}
}

func TestDeltaCompression(t *testing.T) {
	generator := NewV2Generator("/tmp")
	
	// Test delta compression with sequential numbers
	lineNumbers := []uint32{100, 101, 102, 103, 104, 105}
	compressed := generator.compressLineNumbers(lineNumbers)
	
	// Should be much smaller than original
	originalSize := len(lineNumbers) * 4 // 4 bytes per uint32
	compressedSize := len(compressed)
	
	t.Logf("Original size: %d bytes, Compressed size: %d bytes", originalSize, compressedSize)
	t.Logf("Compression ratio: %.2f", float64(originalSize)/float64(compressedSize))
	
	if compressedSize >= originalSize {
		t.Errorf("Compression should reduce size, got %d >= %d", compressedSize, originalSize)
	}
	
	// Test with sparse numbers
	sparseNumbers := []uint32{100, 200, 300, 400, 500}
	compressedSparse := generator.compressLineNumbers(sparseNumbers)
	sparseSizeRatio := float64(len(sparseNumbers)*4) / float64(len(compressedSparse))
	
	t.Logf("Sparse compression ratio: %.2f", sparseSizeRatio)
	
	// Even sparse numbers should have some compression
	if len(compressedSparse) > len(sparseNumbers)*4 {
		t.Errorf("Compression should not increase size")
	}
}
