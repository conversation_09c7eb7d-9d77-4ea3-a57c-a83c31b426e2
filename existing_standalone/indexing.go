package main

import (
	"compress/gzip"
	"encoding/binary"
	"encoding/csv"
	"fmt"
	"os"
	"strings"
)

// Column definitions for the CSV data
var ColumnsList = []string{
	"timestamp", "source_ip", "destination_ip",
	"source_port", "destination_port", "protocol",
	"action", "rule_name", "rule_id",
	"rule_category", "rule_description", "source_country",
	"destination_country", "source_username", "destination_username",
	"source_mac_address", "destination_mac_address", "source_hostname",
	"destination_hostname", "source_os",
	"destination_os", "source_device_type", "destination_device_type",
	"source_location", "destination_location", "source_latitude",
	"source_longitude", "destination_latitude", "destination_longitude",
	"bytes_sent", "bytes_received", "message",
}

// GenerateIndexes creates binary index files for all columns
func GenerateIndexes(csvFile string, outputDir string) error {
	// Create output directory
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Read CSV data
	file, err := os.Open(csvFile)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %v", err)
	}

	fmt.Printf("Processing %d records across %d columns\n", len(records), len(ColumnsList))

	// Generate index for each column
	for colIndex, columnName := range ColumnsList {
		if colIndex >= len(records[0]) {
			continue
		}

		fmt.Printf("Generating index for column: %s\n", columnName)
		err := GenerateColumnIndex(columnName, records, colIndex, outputDir)
		if err != nil {
			return fmt.Errorf("failed to generate index for %s: %v", columnName, err)
		}
	}

	return nil
}

// GenerateColumnIndex creates a binary index file for a specific column
func GenerateColumnIndex(columnName string, records [][]string, colIndex int, outputDir string) error {
	filename := fmt.Sprintf("%s/%s_ex.txt", outputDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write binary format: [line_number][value_length][value]
	offsetByteSlice := make([]byte, 4)
	sizeByteSlice := make([]byte, 4)

	recordCount := 0
	for lineNum, row := range records {
		if colIndex < len(row) && row[colIndex] != "" {
			value := row[colIndex]

			// Write line number (4 bytes, big-endian)
			binary.BigEndian.PutUint32(offsetByteSlice, uint32(lineNum))
			file.Write(offsetByteSlice)

			// Write value length (4 bytes, big-endian)
			binary.BigEndian.PutUint32(sizeByteSlice, uint32(len(value)))
			file.Write(sizeByteSlice)

			// Write value
			file.Write([]byte(value))

			recordCount++
		}
	}

	fmt.Printf("  Generated %d records for %s\n", recordCount, columnName)
	return nil
}

// IndexStats provides statistics about generated indexes
type IndexStats struct {
	ColumnName  string
	RecordCount int
	FileSize    int64
	FilePath    string
}

// GetIndexStats returns statistics for all generated indexes
func GetIndexStats(indexDir string) ([]IndexStats, error) {
	var stats []IndexStats

	for _, columnName := range ColumnsList {
		filename := fmt.Sprintf("%s/%s_ex.txt", indexDir, columnName)

		fileInfo, err := os.Stat(filename)
		if err != nil {
			continue // Skip if file doesn't exist
		}

		// Count records by reading the file
		recordCount, err := countRecordsInIndex(filename)
		if err != nil {
			recordCount = -1 // Mark as error
		}

		stats = append(stats, IndexStats{
			ColumnName:  columnName,
			RecordCount: recordCount,
			FileSize:    fileInfo.Size(),
			FilePath:    filename,
		})
	}

	return stats, nil
}

// countRecordsInIndex counts the number of records in a binary index file
func countRecordsInIndex(filename string) (int, error) {
	file, err := os.Open(filename)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	count := 0
	buffer := make([]byte, 8) // 4 bytes for line number + 4 bytes for length

	for {
		// Read line number and value length
		_, err := file.Read(buffer)
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			return 0, err
		}

		// Extract value length
		valueLen := binary.BigEndian.Uint32(buffer[4:8])
		if valueLen == 0 {
			break
		}

		// Skip the value bytes
		_, err = file.Seek(int64(valueLen), 1) // Seek relative to current position
		if err != nil {
			return 0, err
		}

		count++
	}

	return count, nil
}

// PrintIndexStats displays statistics about generated indexes
func PrintIndexStats(indexDir string) {
	stats, err := GetIndexStats(indexDir)
	if err != nil {
		fmt.Printf("Error getting index stats: %v\n", err)
		return
	}

	fmt.Println("\n📊 Index Statistics")
	fmt.Println("==================")
	fmt.Printf("%-25s %-10s %-12s %-15s\n", "Column", "Records", "Size (KB)", "File")
	fmt.Println(strings.Repeat("-", 70))

	totalSize := int64(0)
	totalRecords := 0

	for _, stat := range stats {
		sizeKB := float64(stat.FileSize) / 1024
		fmt.Printf("%-25s %-10d %-12.1f %-15s\n",
			stat.ColumnName, stat.RecordCount, sizeKB, stat.FilePath)

		totalSize += stat.FileSize
		if stat.RecordCount > 0 {
			totalRecords += stat.RecordCount
		}
	}

	fmt.Println(strings.Repeat("-", 70))
	fmt.Printf("%-25s %-10d %-12.1f\n", "TOTAL", totalRecords, float64(totalSize)/1024)
}

// GenerateCompressedIndexes creates compressed index files (ExistingInx format)
func GenerateCompressedIndexes(csvFile string, outputDir string) error {
	// Create output directory
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Read CSV data
	file, err := os.Open(csvFile)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to read CSV: %v", err)
	}

	fmt.Printf("Processing %d records across %d columns (Compressed)\n", len(records), len(ColumnsList))

	// Generate compressed index for each column
	for colIndex, columnName := range ColumnsList {
		if colIndex >= len(records[0]) {
			continue
		}

		fmt.Printf("Generating compressed index for column: %s\n", columnName)
		err := GenerateCompressedColumnIndex(columnName, records, colIndex, outputDir)
		if err != nil {
			return fmt.Errorf("failed to generate compressed index for %s: %v", columnName, err)
		}
	}

	return nil
}

// GenerateCompressedColumnIndex creates a compressed binary index file for a specific column
func GenerateCompressedColumnIndex(columnName string, records [][]string, colIndex int, outputDir string) error {
	filename := fmt.Sprintf("%s/%s_inx.txt", outputDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create gzip writer for compression
	gzipWriter := gzip.NewWriter(file)
	defer gzipWriter.Close()

	// Write binary format: [line_number][value_length][value] with gzip compression
	offsetByteSlice := make([]byte, 4)
	sizeByteSlice := make([]byte, 4)

	recordCount := 0
	for lineNum, row := range records {
		if colIndex < len(row) && row[colIndex] != "" {
			value := row[colIndex]

			// Write line number (4 bytes, big-endian)
			binary.BigEndian.PutUint32(offsetByteSlice, uint32(lineNum))
			gzipWriter.Write(offsetByteSlice)

			// Write value length (4 bytes, big-endian)
			binary.BigEndian.PutUint32(sizeByteSlice, uint32(len(value)))
			gzipWriter.Write(sizeByteSlice)

			// Write value
			gzipWriter.Write([]byte(value))

			recordCount++
		}
	}

	fmt.Printf("  Generated %d compressed records for %s\n", recordCount, columnName)
	return nil
}
