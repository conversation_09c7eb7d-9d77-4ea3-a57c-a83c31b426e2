package main

import (
	"fmt"
	"log"
)

// This example demonstrates basic usage of the Existing Binary Format Indexing

func main() {
	fmt.Println("🚀 Existing Binary Format Indexing - Basic Usage Example")
	fmt.Println("=======================================================")

	// Example 1: Generate indexes from CSV data
	fmt.Println("\n📋 Step 1: Generate Binary Indexes")
	fmt.Println("----------------------------------")
	
	csvFile := "../data/mock_data.csv"
	indexDir := "../data/indexes"
	
	fmt.Printf("Generating indexes from %s to %s\n", csvFile, indexDir)
	
	// Note: In a real scenario, you would have actual CSV data
	// err := GenerateIndexes(csvFile, indexDir)
	// if err != nil {
	//     log.Fatalf("Failed to generate indexes: %v", err)
	// }
	
	fmt.Println("✅ Index generation completed (simulated)")

	// Example 2: Perform queries
	fmt.Println("\n🔍 Step 2: Perform Queries")
	fmt.Println("-------------------------")
	
	queries := []struct {
		column string
		value  string
		desc   string
	}{
		{"source_username", "odjordjevicrp", "Find specific user"},
		{"protocol", "TCP", "Find TCP connections"},
	}
	
	for _, query := range queries {
		fmt.Printf("\n🔹 %s: %s=%s\n", query.desc, query.column, query.value)
		
		// Note: In a real scenario with actual index files:
		// results := QueryWithResults(query.column, query.value)
		// fmt.Printf("Found %d results\n", len(results))
		
		// For this example, we'll simulate the results
		fmt.Printf("Found results (simulated)\n")
		
		// Example of accessing result data:
		// for i, result := range results {
		//     if i >= 3 { break } // Show first 3
		//     fmt.Printf("  Line %d: %s=%s, %s=%s\n", 
		//         result.LineNumber,
		//         "timestamp", result.Columns["timestamp"],
		//         "source_ip", result.Columns["source_ip"])
		// }
	}

	// Example 3: Performance benchmarking
	fmt.Println("\n⚡ Step 3: Performance Benchmarking")
	fmt.Println("----------------------------------")
	
	fmt.Println("Running performance benchmarks...")
	
	// Note: In a real scenario:
	// BenchmarkQuery("source_username", "odjordjevicrp", 100)
	
	fmt.Println("✅ Benchmark completed (simulated)")
	
	// Example 4: Index statistics
	fmt.Println("\n📊 Step 4: Index Statistics")
	fmt.Println("---------------------------")
	
	fmt.Println("Index statistics:")
	fmt.Println("  - Binary format with 4-byte line numbers")
	fmt.Println("  - Efficient storage for exact string matching")
	fmt.Println("  - Performance: ~570µs for low selectivity queries")
	fmt.Println("  - Memory usage: Moderate (streaming reads)")
	
	// Note: In a real scenario:
	// PrintIndexStats(indexDir)

	fmt.Println("\n🎉 Basic usage example completed!")
	fmt.Println("\nNext steps:")
	fmt.Println("  1. Prepare your CSV data file")
	fmt.Println("  2. Run: go run . generate data.csv indexes/")
	fmt.Println("  3. Run: go run . query column_name search_value")
	fmt.Println("  4. Run: go test -bench=. -benchmem")
}

// Example function showing API usage patterns
func ExampleAPIUsage() {
	// This function demonstrates the main API patterns
	
	// 1. Index Generation
	// err := GenerateIndexes("data.csv", "indexes/")
	
	// 2. Simple Query
	// offsets := GetOffsets("protocol", "TCP")
	
	// 3. Complete Query with Results
	// results := QueryWithResults("source_username", "user123")
	
	// 4. Access Result Data
	// for _, result := range results {
	//     lineNum := result.LineNumber
	//     timestamp := result.Columns["timestamp"]
	//     sourceIP := result.Columns["source_ip"]
	//     // ... process data
	// }
	
	// 5. Performance Benchmarking
	// BenchmarkQuery("protocol", "TCP", 1000)
	
	// 6. Index Statistics
	// PrintIndexStats("indexes/")
}

// Example configuration and customization
func ExampleConfiguration() {
	// The system can be configured by modifying these variables:
	
	// Selected columns to return in results
	// SelectedColumns = []string{"timestamp", "source_ip", "destination_ip", "message"}
	
	// Column definitions for CSV processing
	// ColumnsList = []string{"timestamp", "source_ip", "destination_ip", ...}
	
	// Index file naming pattern: {column}_ex.txt
	// Query file pattern: indexes/{column}_ex.txt
}

// Example error handling patterns
func ExampleErrorHandling() {
	// Proper error handling patterns for the API
	
	// Index generation with error handling
	// err := GenerateIndexes("data.csv", "indexes/")
	// if err != nil {
	//     log.Printf("Index generation failed: %v", err)
	//     return
	// }
	
	// Query with error checking
	// results := QueryWithResults("column", "value")
	// if len(results) == 0 {
	//     log.Println("No results found")
	//     return
	// }
	
	// Validate result structure
	// for _, result := range results {
	//     if result.LineNumber == 0 {
	//         log.Printf("Invalid result: %+v", result)
	//         continue
	//     }
	//     // Process valid result
	// }
}

func init() {
	// This example can be run with: go run examples/basic_usage.go
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}
