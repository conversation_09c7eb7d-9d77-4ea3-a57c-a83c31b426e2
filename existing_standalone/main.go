package main

import (
	"fmt"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "generate":
		if len(os.Args) < 4 {
			fmt.Println("Usage: go run . generate <csv_file> <output_dir>")
			return
		}
		csvFile := os.Args[2]
		outputDir := os.Args[3]

		fmt.Printf("Generating indexes from %s to %s\n", csvFile, outputDir)
		err := GenerateIndexes(csvFile, outputDir)
		if err != nil {
			fmt.Printf("Error generating indexes: %v\n", err)
			return
		}
		fmt.Println("Index generation completed successfully!")

	case "generate-compressed":
		if len(os.Args) < 4 {
			fmt.Println("Usage: go run . generate-compressed <csv_file> <output_dir>")
			return
		}
		csvFile := os.Args[2]
		outputDir := os.Args[3]

		fmt.Printf("Generating compressed indexes from %s to %s\n", csvFile, outputDir)
		err := GenerateCompressedIndexes(csvFile, outputDir)
		if err != nil {
			fmt.Printf("Error generating compressed indexes: %v\n", err)
			return
		}
		fmt.Println("Compressed index generation completed successfully!")

	case "query":
		if len(os.Args) < 4 {
			fmt.Println("Usage: go run . query <column_name> <search_value>")
			fmt.Println("   OR: go run . query <index_dir> <column_name> <search_value>")
			return
		}

		var indexDir, columnName, searchValue string

		if len(os.Args) >= 5 {
			// Format: query <index_dir> <column_name> <search_value>
			indexDir = os.Args[2]
			columnName = os.Args[3]
			searchValue = os.Args[4]
		} else {
			// Format: query <column_name> <search_value> (use default directory)
			indexDir = "data/indexes"
			columnName = os.Args[2]
			searchValue = os.Args[3]
		}

		fmt.Printf("Searching for %s=%s in %s\n", columnName, searchValue, indexDir)
		results := QueryWithResultsFromDir(indexDir, columnName, searchValue)

		fmt.Printf("Found %d results:\n", len(results))
		for i, result := range results {
			if i < 10 { // Show first 10 results
				fmt.Printf("Line %d: %v\n", result.LineNumber, result.Columns)
			}
		}
		if len(results) > 10 {
			fmt.Printf("... and %d more results\n", len(results)-10)
		}

	case "benchmark":
		fmt.Println("Running performance benchmarks...")
		RunBenchmarks()

	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("Existing Binary Format Indexing - Standalone Implementation")
	fmt.Println("===========================================================")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  generate <csv_file> <output_dir>           - Generate binary indexes")
	fmt.Println("  generate-compressed <csv_file> <output_dir> - Generate compressed indexes")
	fmt.Println("  query <column> <value>                     - Search for specific value (default dir)")
	fmt.Println("  query <index_dir> <column> <value>         - Search for specific value (custom dir)")
	fmt.Println("  benchmark                                  - Run performance tests")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  go run . generate data/mock_data.csv data/indexes/")
	fmt.Println("  go run . generate-compressed data/mock_data.csv data/indexes_compressed/")
	fmt.Println("  go run . query source_username odjordjevicrp")
	fmt.Println("  go run . query ../demo_results/existing protocol TCP")
	fmt.Println("  go run . benchmark")
}
