package main

import (
	"testing"
)

// Test data for benchmarks
var testQueries = []struct {
	column string
	value  string
	desc   string
}{
	{"source_username", "odjordjevicrp", "Low selectivity"},
	{"protocol", "TCP", "High selectivity"},
}

// BenchmarkExistingQuery benchmarks the complete query process
func BenchmarkExistingQuery(b *testing.B) {
	for _, query := range testQueries {
		b.Run(query.desc+"_"+query.column, func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				results := QueryWithResults(query.column, query.value)
				_ = results // Prevent optimization
			}
		})
	}
}

// BenchmarkExistingSearchOnly benchmarks only the search operation
func BenchmarkExistingSearchOnly(b *testing.B) {
	for _, query := range testQueries {
		b.Run(query.desc+"_"+query.column, func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				offsets := GetOffsets(query.column, query.value)
				_ = offsets // Prevent optimization
			}
		})
	}
}

// BenchmarkExistingResultRetrieval benchmarks only the result retrieval
func BenchmarkExistingResultRetrieval(b *testing.B) {
	// Pre-compute offsets for the benchmark
	offsets := GetOffsets("source_username", "odjordjevicrp")
	
	b.Run("ResultRetrieval", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			results := GetCompleteRows("data/indexes", offsets)
			_ = results // Prevent optimization
		}
	})
}

// BenchmarkExistingMemoryUsage benchmarks with memory allocation tracking
func BenchmarkExistingMemoryUsage(b *testing.B) {
	b.Run("MemoryUsage", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			results := QueryWithResults("source_username", "odjordjevicrp")
			_ = results
		}
	})
}

// TestQueryCorrectness validates that queries return expected results
func TestQueryCorrectness(t *testing.T) {
	for _, query := range testQueries {
		t.Run(query.column+"_"+query.value, func(t *testing.T) {
			results := QueryWithResults(query.column, query.value)
			
			if len(results) == 0 {
				t.Logf("No results found for %s=%s", query.column, query.value)
				return
			}
			
			// Validate result structure
			for i, result := range results {
				if result.LineNumber == 0 {
					t.Errorf("Result %d has invalid line number: %d", i, result.LineNumber)
				}
				
				if len(result.Columns) == 0 {
					t.Errorf("Result %d has no columns", i)
				}
				
				// Check that selected columns are present
				for _, colName := range SelectedColumns {
					if _, exists := result.Columns[colName]; !exists {
						t.Errorf("Result %d missing column: %s", i, colName)
					}
				}
				
				// Only check first few results to avoid spam
				if i >= 5 {
					break
				}
			}
			
			t.Logf("Query %s=%s returned %d valid results", query.column, query.value, len(results))
		})
	}
}

// TestIndexGeneration tests the index generation process
func TestIndexGeneration(t *testing.T) {
	// This test would require a sample CSV file
	// For now, just test that the function exists and can be called
	t.Run("IndexGenerationAPI", func(t *testing.T) {
		// Test that the function signature is correct
		err := GenerateIndexes("nonexistent.csv", "test_output")
		if err == nil {
			t.Error("Expected error for nonexistent file, got nil")
		}
		t.Logf("Index generation API test passed: %v", err)
	})
}

// BenchmarkIndexGeneration benchmarks the index generation process
func BenchmarkIndexGeneration(b *testing.B) {
	// Skip this benchmark by default since it requires a CSV file
	b.Skip("Skipping index generation benchmark - requires CSV file")
	
	b.Run("IndexGeneration", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			err := GenerateIndexes("data/mock_data.csv", "test_indexes")
			if err != nil {
				b.Fatalf("Index generation failed: %v", err)
			}
		}
	})
}

// Example test showing how to use the API
func ExampleQueryWithResults() {
	// This example shows how to use the query API
	results := QueryWithResults("protocol", "TCP")
	
	for i, result := range results {
		if i >= 3 { // Show only first 3 results
			break
		}
		
		// Access result data
		_ = result.LineNumber
		_ = result.Columns["timestamp"]
		_ = result.Columns["source_ip"]
	}
	
	// Output: Query results with line numbers and selected columns
}
