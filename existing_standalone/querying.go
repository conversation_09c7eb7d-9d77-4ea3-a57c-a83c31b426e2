package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"strings"
	"time"
)

// QueryResult represents a single query result with selected columns
type QueryResult struct {
	LineNumber uint32
	Columns    map[string]string
}

// Selected columns to return in results (configurable)
var SelectedColumns = []string{"timestamp", "source_ip", "destination_ip", "destination_location", "message"}

// GetOffsets finds all line numbers that match the search criteria
func GetOffsets(columnName, searchValue string) []uint32 {
	indexDir := "data/indexes" // Default index directory
	return GetOffsetsFromDir(indexDir, columnName, searchValue)
}

// GetOffsetsFromDir finds line numbers from a specific index directory
func GetOffsetsFromDir(indexDir, columnName, searchValue string) []uint32 {
	var offsets []uint32
	
	filename := fmt.Sprintf("%s/%s_ex.txt", indexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		fmt.Printf("Error opening index file %s: %v\n", filename, err)
		return offsets
	}
	defer file.Close()

	buffer := make([]byte, 8192)
	_, err = file.Read(buffer)
	if err != nil {
		fmt.Printf("Error reading index file: %v\n", err)
		return offsets
	}

	lineAndLen := 4
	start := 0
	var lineNumber, valueLen, valueBuffer []byte

	for {
		start, lineNumber, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}
		start, valueLen, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}

		line := binary.BigEndian.Uint32(lineNumber)
		vLen := binary.BigEndian.Uint32(valueLen)
		if vLen == 0 {
			break
		}

		start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)
		if start == 0 {
			break
		}

		if strings.EqualFold(searchValue, string(valueBuffer)) {
			offsets = append(offsets, line)
		}
	}

	return offsets
}

// getBytes reads bytes from file with buffering (adapted from original implementation)
func getBytes(start, bytesToRead int, buffer []byte, file *os.File) (int, []byte, []byte) {
	var byteData []byte
	var end int

	if start >= len(buffer) {
		start = start - len(buffer)
		file.Seek(int64(start), io.SeekCurrent)
		start = 0
		end = start + bytesToRead
		newBuffer := make([]byte, 8192)
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}
		byteData = append(byteData, newBuffer[start:end]...)
		buffer = newBuffer
		return end, byteData, buffer
	}

	end = start + bytesToRead
	if end > len(buffer) {
		byteData = buffer[start:]
		end = end - len(buffer)
		for {
			if end > 8192 {
				start = 0
				newBuffer := make([]byte, 8192)
				_, err := file.Read(newBuffer)
				if err != nil {
					return 0, byteData, buffer
				}
				byteData = append(byteData, newBuffer...)
				end = end - len(buffer)
			} else {
				break
			}
		}
		start = 0
		newBuffer := make([]byte, 8192)
		_, err := file.Read(newBuffer)
		if err != nil {
			return 0, byteData, buffer
		}
		byteData = append(byteData, newBuffer[start:end]...)
		buffer = newBuffer
		return end, byteData, buffer
	}

	byteData = buffer[start:end]
	return end, byteData, buffer
}

// QueryWithResults performs a complete query and returns structured results
func QueryWithResults(columnName, searchValue string) []QueryResult {
	return QueryWithResultsFromDir("data/indexes", columnName, searchValue)
}

// QueryWithResultsFromDir performs a complete query from a specific directory
func QueryWithResultsFromDir(indexDir, columnName, searchValue string) []QueryResult {
	// Get matching line numbers
	offsets := GetOffsetsFromDir(indexDir, columnName, searchValue)
	
	// Retrieve complete rows for each offset
	return GetCompleteRows(indexDir, offsets)
}

// GetCompleteRows retrieves complete rows for given line numbers
func GetCompleteRows(indexDir string, offsets []uint32) []QueryResult {
	var results []QueryResult

	for _, offset := range offsets {
		result := QueryResult{
			LineNumber: offset,
			Columns:    make(map[string]string),
		}

		// Get data for each selected column
		for _, colName := range SelectedColumns {
			value := GetValueForLineNumber(indexDir, colName, offset)
			result.Columns[colName] = value
		}

		results = append(results, result)
	}

	return results
}

// GetValueForLineNumber retrieves a specific value for a given line number and column
func GetValueForLineNumber(indexDir, columnName string, lineNumber uint32) string {
	filename := fmt.Sprintf("%s/%s_ex.txt", indexDir, columnName)
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Sprintf("ERROR: %v", err)
	}
	defer file.Close()

	buffer := make([]byte, 8192)
	_, err = file.Read(buffer)
	if err != nil {
		return fmt.Sprintf("ERROR: %v", err)
	}

	lineAndLen := 4
	start := 0
	var lineNumberBytes, valueLen, valueBuffer []byte

	for {
		start, lineNumberBytes, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}
		start, valueLen, buffer = getBytes(start, lineAndLen, buffer, file)
		if start == 0 {
			break
		}

		line := binary.BigEndian.Uint32(lineNumberBytes)
		vLen := binary.BigEndian.Uint32(valueLen)
		if vLen == 0 {
			break
		}

		start, valueBuffer, buffer = getBytes(start, int(vLen), buffer, file)
		if start == 0 {
			break
		}

		if line == lineNumber {
			return string(valueBuffer)
		}
	}

	return "" // Not found
}

// BenchmarkQuery performs a simple performance benchmark
func BenchmarkQuery(columnName, searchValue string, iterations int) {
	fmt.Printf("🔄 Benchmarking query: %s=%s (%d iterations)\n", columnName, searchValue, iterations)
	
	start := time.Now()
	var totalResults int
	
	for i := 0; i < iterations; i++ {
		results := QueryWithResults(columnName, searchValue)
		totalResults = len(results)
	}
	
	duration := time.Since(start)
	avgTime := duration / time.Duration(iterations)
	
	fmt.Printf("📊 Results:\n")
	fmt.Printf("  Total time: %v\n", duration)
	fmt.Printf("  Average time per query: %v\n", avgTime)
	fmt.Printf("  Results found: %d\n", totalResults)
	fmt.Printf("  Queries per second: %.2f\n", float64(iterations)/duration.Seconds())
}

// RunBenchmarks runs a set of predefined benchmarks
func RunBenchmarks() {
	fmt.Println("🚀 Running Existing Binary Format Benchmarks")
	fmt.Println("============================================")
	
	benchmarks := []struct {
		column string
		value  string
		desc   string
	}{
		{"source_username", "odjordjevicrp", "Low selectivity query"},
		{"protocol", "TCP", "High selectivity query"},
	}
	
	for _, bench := range benchmarks {
		fmt.Printf("\n📋 %s\n", bench.desc)
		BenchmarkQuery(bench.column, bench.value, 100)
	}
	
	fmt.Println("\n✅ Benchmark completed!")
}
