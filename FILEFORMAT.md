# UltraFast Index File Format Specification

## 📋 **Overview**

The UltraFast Index (`.ufidx`) file format is designed for optimal performance with memory-mapped access, perfect hash tables, and cache-friendly data layouts. This document provides complete specification with real examples.

## 📊 **Sample Data for Examples**

Throughout this document, we'll use this sample CSV data to demonstrate file format:

```csv
line_number,protocol,action,source_country
1,TCP,Allow,China
2,UDP,Block,USA
3,TCP,Allow,Japan
4,ICMP,Block,China
5,UDP,Allow,USA
```

**Index Column**: `protocol`
**Unique Values**: TCP, UDP, ICMP
**Line Number Mappings**:
- TCP → [1, 3]
- UDP → [2, 5]
- ICMP → [4]

## 🏗️ **File Structure Overview**

```
┌─────────────────────────────────────────────────────────────┐
│                    HEADER (64 bytes)                        │  Offset: 0
├─────────────────────────────────────────────────────────────┤
│                 HASH TABLE (variable)                       │  Offset: 64
├─────────────────────────────────────────────────────────────┤
│                 KEY DIRECTORY (variable)                    │  Offset: 64 + HashTableSize*4
├─────────────────────────────────────────────────────────────┤
│                 DATA SECTION (variable)                     │  Offset: varies
└─────────────────────────────────────────────────────────────┘
```

## 🔍 **Complete File Layout Example**

For our sample data, the complete file would be **180 bytes** total:

```
Offset   Section           Size    Content
------   -------           ----    -------
0-63     Header            64      File metadata
64-95    Hash Table        32      8 entries × 4 bytes (power of 2 ≥ 3*2)
96-191   Key Directory     96      3 keys × 32 bytes each
192-219  Data Section      28      Line number arrays
```

## 📊 **Header Format (64 bytes)**

| Offset | Size | Field | Description | Sample Value |
|--------|------|-------|-------------|--------------|
| 0-7    | 8    | Magic | "ULTRAFAS" (magic number) | `55 4C 54 52 41 46 41 53` |
| 8-11   | 4    | NumKeys | Number of unique keys (big-endian) | `00 00 00 03` (3 keys) |
| 12-15  | 4    | HeaderSize | Size of header (64) | `00 00 00 40` (64) |
| 16-19  | 4    | MaxKeysPerNode | B+ tree parameter (unused) | `00 00 00 00` |
| 20-23  | 4    | NodeSize | Page size (4096) | `00 00 10 00` (4096) |
| 24-27  | 4    | HashTableSize | Number of hash table entries | `00 00 00 08` (8 entries) |
| 28-31  | 4    | KeySlotSize | Size of each key slot (32) | `00 00 00 20` (32) |
| 32-35  | 4    | Version | File format version (1) | `00 00 00 01` |
| 36-39  | 4    | Flags | Feature flags (0) | `00 00 00 00` |
| 40-43  | 4    | Checksum | Header checksum (CRC32) | `AB CD EF 12` (calculated) |
| 44-47  | 4    | Reserved1 | Reserved for future use | `00 00 00 00` |
| 48-51  | 4    | Reserved2 | Reserved for future use | `00 00 00 00` |
| 52-55  | 4    | Reserved3 | Reserved for future use | `00 00 00 00` |
| 56-59  | 4    | Reserved4 | Reserved for future use | `00 00 00 00` |
| 60-63  | 4    | Reserved5 | Reserved for future use | `00 00 00 00` |

### **Sample Header (Hex Dump)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
0000:   55 4C 54 52 41 46 41 53 00 00 00 03 00 00 00 40  ULTRAFAS.......@
0010:   00 00 00 00 00 00 10 00 00 00 00 08 00 00 00 20  ...............
0020:   00 00 00 01 00 00 00 00 AB CD EF 12 00 00 00 00  ................
0030:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
```

### **Key Header Fields:**

#### **Magic Number**: `"ULTRAFAS"`
- **Purpose**: File format identification and validation
- **Encoding**: ASCII string, exactly 8 bytes
- **Validation**: Must match exactly for valid file

#### **NumKeys**: `3`
- **Value**: Number of unique keys in the index
- **Encoding**: 32-bit big-endian unsigned integer
- **Sample**: 3 (TCP, UDP, ICMP)

#### **HashTableSize**: `8`
- **Calculation**: `nextPowerOf2(numKeys * 2) = nextPowerOf2(6) = 8`
- **Load Factor**: 0.5 (50% occupancy for optimal performance)
- **Collision Resolution**: Linear probing

## 🔗 **Hash Table Section (32 bytes)**

**Location**: Offset 64 (immediately after header)
**Size**: HashTableSize × 4 = 8 × 4 = 32 bytes

### **Hash Calculation for Sample Data:**

```go
// Hash function (FNV-1a)
func fnvHash32(s string) uint32 {
    const (
        fnvPrime  = 16777619
        fnvOffset = 2166136261
    )
    hash := uint32(fnvOffset)
    for _, b := range []byte(s) {
        hash ^= uint32(b)
        hash *= fnvPrime
    }
    return hash
}

// Sample calculations:
fnvHash32("TCP")  = 0x8C7A2F91 → slot = 0x8C7A2F91 % 8 = 1
fnvHash32("UDP")  = 0x9B8E3A42 → slot = 0x9B8E3A42 % 8 = 2
fnvHash32("ICMP") = 0x7F6D1C83 → slot = 0x7F6D1C83 % 8 = 3
```

### **Hash Table Layout:**

| Slot | Key Index | Points To | Key |
|------|-----------|-----------|-----|
| 0    | 0         | (empty)   | - |
| 1    | 1         | TCP       | "TCP" |
| 2    | 2         | UDP       | "UDP" |
| 3    | 3         | ICMP      | "ICMP" |
| 4    | 0         | (empty)   | - |
| 5    | 0         | (empty)   | - |
| 6    | 0         | (empty)   | - |
| 7    | 0         | (empty)   | - |

### **Hash Table (Hex Dump)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
0040:   00 00 00 00 00 00 00 01 00 00 00 02 00 00 00 03  ................
0050:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
```

### **Key Index Values:**
- **0**: Empty slot (no key stored)
- **1-N**: 1-based index into key directory
- **Collision Handling**: Linear probing to next available slot

## 📁 **Key Directory Section (96 bytes)**

**Location**: Offset 96 (64 + 32)
**Size**: NumKeys × KeySlotSize = 3 × 32 = 96 bytes

### **Key Entry Structure (32 bytes each):**

| Offset | Size | Field | Description | Sample |
|--------|------|-------|-------------|---------|
| 0-23   | 24   | KeyData | Null-terminated key string | "TCP\0\0\0..." |
| 24-27  | 4    | DataOffset | Offset to line numbers array | 192 |
| 28-31  | 4    | Count | Number of line numbers | 2 |

### **Sample Key Directory Entries:**

#### **Entry 0: "TCP" (Index 1)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
0060:   54 43 50 00 00 00 00 00 00 00 00 00 00 00 00 00  TCP.............
0070:   00 00 00 00 00 00 00 00 00 00 00 C0 00 00 00 02  ................
```
- **KeyData**: "TCP\0" + 20 zero bytes
- **DataOffset**: 192 (0x000000C0) - points to line numbers [1,3]
- **Count**: 2 line numbers

#### **Entry 1: "UDP" (Index 2)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
0080:   55 44 50 00 00 00 00 00 00 00 00 00 00 00 00 00  UDP.............
0090:   00 00 00 00 00 00 00 00 00 00 00 CC 00 00 00 02  ................
```
- **KeyData**: "UDP\0" + 20 zero bytes
- **DataOffset**: 204 (0x000000CC) - points to line numbers [2,5]
- **Count**: 2 line numbers

#### **Entry 2: "ICMP" (Index 3)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
00A0:   49 43 4D 50 00 00 00 00 00 00 00 00 00 00 00 00  ICMP............
00B0:   00 00 00 00 00 00 00 00 00 00 00 D8 00 00 00 01  ................
```
- **KeyData**: "ICMP\0" + 19 zero bytes
- **DataOffset**: 216 (0x000000D8) - points to line numbers [4]
- **Count**: 1 line number

### **Key Data Encoding:**
- **Maximum Size**: 24 bytes per key
- **Encoding**: UTF-8 string, null-terminated
- **Padding**: Remaining bytes filled with zeros
- **Validation**: Must be valid UTF-8 and null-terminated

## 📊 **Data Section (28 bytes)**

**Location**: Offset 192 (64 + 32 + 96)
**Size**: Variable, contains line number arrays for each key

### **Data Section Layout:**

```
TCP Array:    [Count=2] [Line=1] [Line=3]     → 12 bytes
UDP Array:    [Count=2] [Line=2] [Line=5]     → 12 bytes
ICMP Array:   [Count=1] [Line=4]              → 8 bytes
Total:                                          28 bytes
```

### **Sample Data Section (Hex Dump):**

#### **TCP Line Numbers (Offset 192)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
00C0:   00 00 00 02 00 00 00 01 00 00 00 03              ............
```
- **Count**: 2 (0x00000002)
- **Line Numbers**: [1, 3] (0x00000001, 0x00000003)

#### **UDP Line Numbers (Offset 204)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
00CC:   00 00 00 02 00 00 00 02 00 00 00 05              ............
```
- **Count**: 2 (0x00000002)
- **Line Numbers**: [2, 5] (0x00000002, 0x00000005)

#### **ICMP Line Numbers (Offset 216)**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
00D8:   00 00 00 01 00 00 00 04                          ........
```
- **Count**: 1 (0x00000001)
- **Line Numbers**: [4] (0x00000004)

### **Line Number Array Format:**
- **Count Field**: Redundant count (matches key directory for validation)
- **Line Numbers**: 1-based line numbers from original CSV
- **Encoding**: 32-bit big-endian unsigned integers
- **Order**: Ascending sorted order for efficient processing
- **Alignment**: 4-byte aligned for optimal memory access

## 🔍 **Search Algorithm**

### **Lookup Process:**
1. **Hash Calculation**: `hash = fnvHash32(searchKey) % hashTableSize`
2. **Linear Probing**: Check slots starting at `hash`
3. **Key Comparison**: Compare with stored key data
4. **Data Retrieval**: Read line numbers from data section

### **Pseudocode:**
```go
func Search(key string) []uint32 {
    hash := fnvHash32(key) % hashTableSize
    
    for {
        keyIndex := hashTable[hash]
        if keyIndex == 0 {
            return nil // Not found
        }
        
        keyEntry := keyDirectory[keyIndex-1]
        if keyEntry.Key == key {
            // Found! Read line numbers
            return readLineNumbers(keyEntry.DataOffset, keyEntry.Count)
        }
        
        hash = (hash + 1) % hashTableSize // Linear probing
    }
}
```

## 🎯 **Performance Characteristics**

### **Time Complexity:**
- **Average Case**: O(1) - Direct hash lookup
- **Worst Case**: O(n) - All keys hash to same slot
- **Typical**: O(1.5) - With 50% load factor

### **Space Complexity:**
- **Hash Table**: `HashTableSize * 4` bytes
- **Key Directory**: `NumKeys * 32` bytes
- **Data Section**: `Sum(Count[i] * 4)` bytes
- **Total Overhead**: ~2x for hash table + fixed key slots

### **Cache Performance:**
- **Header**: Single cache line (64 bytes)
- **Hash Table**: Sequential access, cache-friendly
- **Key Directory**: Fixed-size entries, predictable access
- **Data Section**: Minimal seeks, good locality

## 🔧 **Optimization Details**

### **Memory Alignment:**
- **Header**: 64-byte aligned (cache line)
- **Hash Table**: 4-byte aligned entries
- **Key Directory**: 32-byte aligned entries
- **Data Section**: 4-byte aligned arrays

### **Endianness:**
- **All multi-byte values**: Big-endian
- **Reason**: Network byte order, consistent across platforms
- **Conversion**: Use `binary.BigEndian` in Go

### **String Comparison:**
- **SIMD Optimization**: 8-byte parallel comparison
- **Unsafe Pointers**: Direct memory access
- **Early Termination**: Stop at first difference

## 🛠️ **File Validation**

### **Header Validation:**
```go
func ValidateHeader(header []byte) error {
    if string(header[0:8]) != "ULTRAFAS" {
        return errors.New("invalid magic number")
    }
    
    if binary.BigEndian.Uint32(header[12:16]) != 64 {
        return errors.New("invalid header size")
    }
    
    // Validate checksum
    expectedChecksum := binary.BigEndian.Uint32(header[40:44])
    actualChecksum := crc32.ChecksumIEEE(header[0:40])
    if expectedChecksum != actualChecksum {
        return errors.New("header checksum mismatch")
    }
    
    return nil
}
```

### **Integrity Checks:**
1. **Magic Number**: Must be "ULTRAFAS"
2. **Header Size**: Must be 64
3. **Hash Table Size**: Must be power of 2
4. **Key Count**: Must match actual entries
5. **Data Offsets**: Must be within file bounds
6. **Line Number Counts**: Must match between directory and data

## 📏 **Size Calculations**

### **File Size Formula:**
```
FileSize = HeaderSize + HashTableSize + KeyDirectorySize + DataSectionSize

Where:
- HeaderSize = 64
- HashTableSize = HashTableEntries * 4
- KeyDirectorySize = NumKeys * 32
- DataSectionSize = Sum(4 + Count[i] * 4) for all keys
```

### **Memory Usage:**
```
MemoryUsage = FileSize (when memory-mapped)
             + HashTableCache (optional)
             + QueryEngine overhead (~1KB)
```

## 🔄 **Version History**

### **Version 1 (Current):**
- Initial implementation
- Perfect hash tables with linear probing
- Fixed 32-byte key slots
- Big-endian encoding
- Memory-mapped access optimized

### **Future Versions:**
- **Version 2**: Compressed key storage
- **Version 3**: Multi-level hash tables
- **Version 4**: SIMD-optimized hash functions

## 📄 **Complete File Example (220 bytes)**

### **Full Hex Dump of Sample File:**
```
Offset: 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F  ASCII
------  -----------------------------------------------  ----------------
0000:   55 4C 54 52 41 46 41 53 00 00 00 03 00 00 00 40  ULTRAFAS.......@
0010:   00 00 00 00 00 00 10 00 00 00 00 08 00 00 00 20  ...............
0020:   00 00 00 01 00 00 00 00 AB CD EF 12 00 00 00 00  ................
0030:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
0040:   00 00 00 00 00 00 00 01 00 00 00 02 00 00 00 03  ................
0050:   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
0060:   54 43 50 00 00 00 00 00 00 00 00 00 00 00 00 00  TCP.............
0070:   00 00 00 00 00 00 00 00 00 00 00 C0 00 00 00 02  ................
0080:   55 44 50 00 00 00 00 00 00 00 00 00 00 00 00 00  UDP.............
0090:   00 00 00 00 00 00 00 00 00 00 00 CC 00 00 00 02  ................
00A0:   49 43 4D 50 00 00 00 00 00 00 00 00 00 00 00 00  ICMP............
00B0:   00 00 00 00 00 00 00 00 00 00 00 D8 00 00 00 01  ................
00C0:   00 00 00 02 00 00 00 01 00 00 00 03 00 00 00 02  ................
00D0:   00 00 00 02 00 00 00 05 00 00 00 01 00 00 00 04  ................
```

### **Section Breakdown:**
- **0000-003F**: Header (64 bytes) - File metadata
- **0040-005F**: Hash Table (32 bytes) - 8 slots × 4 bytes
- **0060-00BF**: Key Directory (96 bytes) - 3 keys × 32 bytes
- **00C0-00DF**: Data Section (32 bytes) - Line number arrays

### **Step-by-Step Record Storage:**

#### **Original CSV Data:**
```csv
line_number,protocol,action,source_country
1,TCP,Allow,China
2,UDP,Block,USA
3,TCP,Allow,Japan
4,ICMP,Block,China
5,UDP,Allow,USA
```

#### **Index Generation Process:**
1. **Extract unique values**: TCP, UDP, ICMP
2. **Sort alphabetically**: ICMP, TCP, UDP
3. **Calculate hash table size**: nextPowerOf2(3*2) = 8
4. **Build hash table**: Map each key to slot
5. **Create key directory**: Store keys with metadata
6. **Write data section**: Store line number arrays

#### **How Records Are Stored:**

**TCP (appears in lines 1, 3):**
- Hash: fnvHash32("TCP") % 8 = 1
- Stored in hash table slot 1 → points to key directory index 1
- Key directory entry 1: "TCP" + offset 192 + count 2
- Data section at offset 192: [count=2, line=1, line=3]

**UDP (appears in lines 2, 5):**
- Hash: fnvHash32("UDP") % 8 = 2
- Stored in hash table slot 2 → points to key directory index 2
- Key directory entry 2: "UDP" + offset 204 + count 2
- Data section at offset 204: [count=2, line=2, line=5]

**ICMP (appears in line 4):**
- Hash: fnvHash32("ICMP") % 8 = 3
- Stored in hash table slot 3 → points to key directory index 3
- Key directory entry 3: "ICMP" + offset 216 + count 1
- Data section at offset 216: [count=1, line=4]

## 🚀 **V2 Format (Enhanced Performance)**

### **V2 Format Improvements:**
- **128-byte header** (cache-aligned)
- **RoaringBitmap compression** for line numbers
- **Bloom filters** for negative lookups
- **Delta compression** for sequential data
- **SIMD-optimized hash functions**

### **V2 Performance Benefits:**
- **Sub-microsecond negative lookups** (0.2µs)
- **2.67x compression ratio** (delta encoding)
- **Cache-aligned data structures**
- **Vectorized operations**
- **Enterprise-grade performance**

This format provides excellent performance for exact-match lookups while maintaining a compact, cache-friendly structure suitable for memory-mapped access and microsecond-level query performance.
