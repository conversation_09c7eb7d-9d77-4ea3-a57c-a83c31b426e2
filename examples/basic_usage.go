package main

import (
	"fmt"
	"log"
	"os"
	"time"
)

// This example demonstrates basic usage of the UltraFast index
func main() {
	// Example data
	data := []Record{
		{LineNumber: 1, Value: "john_doe"},
		{LineNumber: 2, Value: "jane_smith"},
		{LineNumber: 3, Value: "bob_wilson"},
		{LineNumber: 4, Value: "alice_brown"},
		{LineNumber: 5, Value: "john_doe"}, // Duplicate value
		{LineNumber: 6, Value: "charlie_davis"},
	}

	// Create output directory
	indexDir := "./example_indexes"
	if err := os.MkdirAll(indexDir, 0755); err != nil {
		log.Fatal(err)
	}

	fmt.Println("=== UltraFast Index Basic Usage Example ===")
	fmt.Println()

	// Step 1: Generate index
	fmt.Println("1. Generating index...")
	generator := NewGenerator(indexDir)
	
	start := time.Now()
	if err := generator.Generate("users", data); err != nil {
		log.Fatal(err)
	}
	generateTime := time.Since(start)
	
	fmt.Printf("   ✓ Index generated in %v\n", generateTime)
	
	// Check file size
	filename := fmt.Sprintf("%s/users_ultrafast.ufidx", indexDir)
	if stat, err := os.Stat(filename); err == nil {
		fmt.Printf("   ✓ Index file size: %d bytes\n", stat.Size())
	}
	fmt.Println()

	// Step 2: Create query engine
	fmt.Println("2. Creating query engine...")
	engine := NewQueryEngine(indexDir)
	defer engine.Close()
	fmt.Println("   ✓ Query engine ready")
	fmt.Println()

	// Step 3: Perform searches
	fmt.Println("3. Performing searches...")
	
	searchValues := []string{"john_doe", "jane_smith", "nonexistent_user", "alice_brown"}
	
	for _, searchValue := range searchValues {
		start := time.Now()
		results, err := engine.Search("users", searchValue)
		searchTime := time.Since(start)
		
		if err != nil {
			fmt.Printf("   ❌ Error searching for '%s': %v\n", searchValue, err)
			continue
		}
		
		fmt.Printf("   🔍 Search '%s': %d results in %v\n", 
			searchValue, len(results), searchTime)
		
		if len(results) > 0 {
			fmt.Printf("      Line numbers: %v\n", results)
		}
	}
	fmt.Println()

	// Step 4: Show performance statistics
	fmt.Println("4. Performance statistics:")
	stats := engine.GetStats()
	fmt.Printf("   • Memory mapped: %t\n", stats.MemoryMapped)
	fmt.Printf("   • Index size: %d bytes\n", stats.IndexSize)
	fmt.Printf("   • Total lookups: %d\n", stats.TotalLookups)
	fmt.Printf("   • Hash collisions: %d\n", stats.HashCollisions)
	fmt.Printf("   • Last lookup time: %v\n", stats.AvgLookupTime)
	fmt.Println()

	// Step 5: Demonstrate batch operations
	fmt.Println("5. Batch search demonstration:")
	batchStart := time.Now()
	totalResults := 0
	
	for i := 0; i < 1000; i++ {
		searchValue := searchValues[i%len(searchValues)]
		results, err := engine.Search("users", searchValue)
		if err == nil {
			totalResults += len(results)
		}
	}
	
	batchTime := time.Since(batchStart)
	avgTime := batchTime / 1000
	
	fmt.Printf("   ✓ 1000 searches completed in %v\n", batchTime)
	fmt.Printf("   ✓ Average search time: %v\n", avgTime)
	fmt.Printf("   ✓ Total results found: %d\n", totalResults)
	fmt.Printf("   ✓ Throughput: %.0f queries/second\n", 
		1000.0/batchTime.Seconds())
	fmt.Println()

	fmt.Println("=== Example completed successfully! ===")
	fmt.Println()
	fmt.Println("Key takeaways:")
	fmt.Println("• Index generation is fast and efficient")
	fmt.Println("• Search operations are sub-microsecond")
	fmt.Println("• Memory mapping provides zero-copy access")
	fmt.Println("• Perfect hash tables deliver O(1) performance")
	fmt.Println("• Batch operations scale linearly")
}

// Record represents a data record for indexing
type Record struct {
	LineNumber uint32
	Value      string
}

// Placeholder functions - in real usage, import from ultrafast package
func NewGenerator(dir string) *Generator {
	return &Generator{outputDir: dir}
}

func NewQueryEngine(dir string) *QueryEngine {
	return &QueryEngine{
		indexDir: dir,
		mmapData: make(map[string][]byte),
	}
}

type Generator struct {
	outputDir string
}

func (g *Generator) Generate(column string, data []Record) error {
	// Implementation would be imported from ultrafast package
	fmt.Printf("   Generating index for %d records...\n", len(data))
	return nil
}

type QueryEngine struct {
	indexDir string
	mmapData map[string][]byte
}

func (e *QueryEngine) Search(column, value string) ([]uint32, error) {
	// Implementation would be imported from ultrafast package
	// This is just a mock for the example
	switch value {
	case "john_doe":
		return []uint32{1, 5}, nil
	case "jane_smith":
		return []uint32{2}, nil
	case "alice_brown":
		return []uint32{4}, nil
	default:
		return []uint32{}, nil
	}
}

func (e *QueryEngine) Close() error {
	return nil
}

type Stats struct {
	MemoryMapped   bool
	IndexSize      int64
	TotalLookups   uint64
	HashCollisions uint64
	AvgLookupTime  time.Duration
}

func (e *QueryEngine) GetStats() Stats {
	return Stats{
		MemoryMapped:   true,
		IndexSize:      1024,
		TotalLookups:   4,
		HashCollisions: 0,
		AvgLookupTime:  time.Microsecond * 83,
	}
}
