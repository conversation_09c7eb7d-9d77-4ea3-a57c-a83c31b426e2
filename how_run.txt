# How to Run Three Implementation Queries

## 🔍 Three Independent Query Commands

### 1. Existing Implementation
cd existing_standalone && go run . query-count ../demo_results/existing protocol TCP

### 2. ExistingInx Implementation  
cd existing_standalone && go run . query-count ../demo_results/existing_inx protocol TCP

### 3. Ultrafast (V2) Implementation
go run main.go ultrafast.go ultrafast_v2.go queryparser.go rowstore.go query-v2 ./demo_results/ultrafast_v2 demo_table "protocol=TCP" protocol

## 📋 Alternative Test Queries

You can also test with different filter conditions:

### Medium Selectivity Query:
# Existing
cd existing_standalone && go run . query-count ../demo_results/existing source_username odjordjevicrp

# ExistingInx  
cd existing_standalone && go run . query-count ../demo_results/existing_inx source_username odjordjevicrp

# Ultrafast V2
go run main.go ultrafast.go ultrafast_v2.go queryparser.go rowstore.go query-v2 ./demo_results/ultrafast_v2 demo_table "source_username=odjordjevicrp" source_username

### Low Selectivity Query:
# Existing
cd existing_standalone && go run . query-count ../demo_results/existing action ALLOW

# ExistingInx
cd existing_standalone && go run . query-count ../demo_results/existing_inx action ALLOW

# Ultrafast V2  
go run main.go ultrafast.go ultrafast_v2.go queryparser.go rowstore.go query-v2 ./demo_results/ultrafast_v2 demo_table "action=ALLOW" action

### Geographic Query:
# Existing
cd existing_standalone && go run . query-count ../demo_results/existing destination_country "United States"

# ExistingInx
cd existing_standalone && go run . query-count ../demo_results/existing_inx destination_country "United States"

# Ultrafast V2
go run main.go ultrafast.go ultrafast_v2.go queryparser.go rowstore.go query-v2 ./demo_results/ultrafast_v2 demo_table "destination_country=United States" destination_country

## ⚡ Quick Performance Test

To quickly test all three with timing:

# Test Existing
echo "Testing Existing:" && time (cd existing_standalone && go run . query-count ../demo_results/existing protocol TCP)

# Test ExistingInx  
echo "Testing ExistingInx:" && time (cd existing_standalone && go run . query-count ../demo_results/existing_inx protocol TCP)

# Test Ultrafast V2
echo "Testing Ultrafast V2:" && time go run main.go ultrafast.go ultrafast_v2.go queryparser.go rowstore.go query-v2 ./demo_results/ultrafast_v2 demo_table "protocol=TCP" protocol

## 🚀 Full Demo Script

To run the complete three-way comparison demo:

./run_demo.sh
# Choose option 3 for the three-way comparison

## 📊 Expected Performance Results

Based on the demo results:

Implementation | Gen Time | Storage | Avg Query | QPS    | Speedup
---------------|----------|---------|-----------|--------|--------
Existing       | ~5.3s    | 46.1 MB | ~337ms    | 3.0    | 1.0x
ExistingInx    | ~1.6s    | 7.3 MB  | ~325ms    | 3.1    | 1.0x
Ultrafast      | ~878ms   | 51.0 MB | ~317ms    | 3.2    | 1.1x

Key Insights:
- Ultrafast V2: Fastest index generation and query performance
- ExistingInx: Best storage compression (84% space savings)
- All implementations: Working with realistic performance metrics

## 🔧 Prerequisites

Make sure you have:
1. Go installed
2. mock_data.csv in the root directory
3. Run the demo at least once to generate indexes:
   ./run_demo.sh (option 3)

## 📁 Directory Structure

After running the demo, you'll have:
- demo_results/existing/        (Existing implementation indexes)
- demo_results/existing_inx/    (ExistingInx compressed indexes)  
- demo_results/ultrafast_v2/    (Ultrafast V2 indexes)
