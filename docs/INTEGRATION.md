# UltraFast Index Integration Guide

## 🎯 **Overview**

This guide helps you integrate UltraFast Index into your existing systems for maximum performance gains.

## 🚀 **Quick Integration**

### **1. Replace Existing Index Generation**

**Before (Traditional):**
```go
// Your existing index generation
func generateIndex(data []Record, outputFile string) error {
    // Traditional binary search index
    return createBinaryIndex(data, outputFile)
}
```

**After (UltraFast):**
```go
import "github.com/yourorg/ultrafast"

func generateIndex(data []Record, outputDir string, column string) error {
    generator := ultrafast.NewGenerator(outputDir)
    return generator.Generate(column, data)
}
```

### **2. Replace Search Operations**

**Before (Traditional):**
```go
func searchIndex(indexFile, searchValue string) ([]uint32, error) {
    // Binary search through sorted index
    return binarySearchIndex(indexFile, searchValue)
}
```

**After (UltraFast):**
```go
func searchIndex(indexDir, column, searchValue string) ([]uint32, error) {
    engine := ultrafast.NewQueryEngine(indexDir)
    defer engine.Close()
    return engine.Search(column, searchValue)
}
```

## 🔄 **Migration Strategy**

### **Phase 1: Parallel Deployment**
1. **Generate both indexes** during transition period
2. **Compare results** to ensure accuracy
3. **Monitor performance** of both systems
4. **Gradually shift traffic** to UltraFast

### **Phase 2: Performance Validation**
```go
func validateMigration(data []Record, queries []Query) error {
    // Generate both indexes
    traditionalIndex := generateTraditionalIndex(data)
    ultrafastIndex := generateUltraFastIndex(data)
    
    // Compare results for all queries
    for _, query := range queries {
        traditional := searchTraditional(traditionalIndex, query)
        ultrafast := searchUltraFast(ultrafastIndex, query)
        
        if !compareResults(traditional, ultrafast) {
            return fmt.Errorf("result mismatch for query: %v", query)
        }
    }
    
    return nil
}
```

### **Phase 3: Full Migration**
1. **Switch all traffic** to UltraFast
2. **Remove traditional indexes** after validation period
3. **Monitor production metrics**
4. **Optimize configuration** based on real workload

## 🏗️ **Architecture Integration**

### **Microservices Architecture**

```go
// Index Service
type IndexService struct {
    generator *ultrafast.Generator
    engine    *ultrafast.QueryEngine
}

func (s *IndexService) CreateIndex(req *CreateIndexRequest) error {
    return s.generator.Generate(req.Column, req.Data)
}

func (s *IndexService) Search(req *SearchRequest) (*SearchResponse, error) {
    results, err := s.engine.Search(req.Column, req.Value)
    return &SearchResponse{Results: results}, err
}
```

### **Database Integration**

```go
// Custom database index
type UltraFastDBIndex struct {
    engine *ultrafast.QueryEngine
    column string
}

func (idx *UltraFastDBIndex) Lookup(value interface{}) ([]RowID, error) {
    strValue := fmt.Sprintf("%v", value)
    results, err := idx.engine.Search(idx.column, strValue)
    
    rowIDs := make([]RowID, len(results))
    for i, lineNum := range results {
        rowIDs[i] = RowID(lineNum)
    }
    
    return rowIDs, err
}
```

### **Web API Integration**

```go
// REST API handler
func (h *Handler) SearchHandler(w http.ResponseWriter, r *http.Request) {
    column := r.URL.Query().Get("column")
    value := r.URL.Query().Get("value")
    
    start := time.Now()
    results, err := h.engine.Search(column, value)
    duration := time.Since(start)
    
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    response := SearchResponse{
        Results:  results,
        Count:    len(results),
        Duration: duration.String(),
    }
    
    json.NewEncoder(w).Encode(response)
}
```

## 📊 **Configuration Management**

### **Production Configuration**

```go
// config.yaml
ultrafast:
  index_dir: "/var/lib/ultrafast/indexes"
  hash_table_load_factor: 0.5
  key_slot_size: 32
  cache_line_size: 64
  enable_mmap: true
  prefetch_size: 4096
  
monitoring:
  enable_stats: true
  stats_interval: "1m"
  log_slow_queries: true
  slow_query_threshold: "100µs"
```

```go
// Configuration struct
type Config struct {
    UltraFast struct {
        IndexDir              string  `yaml:"index_dir"`
        HashTableLoadFactor   float64 `yaml:"hash_table_load_factor"`
        KeySlotSize          int     `yaml:"key_slot_size"`
        CacheLineSize        int     `yaml:"cache_line_size"`
        EnableMmap           bool    `yaml:"enable_mmap"`
        PrefetchSize         int     `yaml:"prefetch_size"`
    } `yaml:"ultrafast"`
    
    Monitoring struct {
        EnableStats          bool          `yaml:"enable_stats"`
        StatsInterval        time.Duration `yaml:"stats_interval"`
        LogSlowQueries       bool          `yaml:"log_slow_queries"`
        SlowQueryThreshold   time.Duration `yaml:"slow_query_threshold"`
    } `yaml:"monitoring"`
}
```

## 🔧 **Performance Tuning**

### **Memory Management**

```go
// Connection pooling for query engines
type EnginePool struct {
    engines chan *ultrafast.QueryEngine
    indexDir string
}

func NewEnginePool(indexDir string, size int) *EnginePool {
    pool := &EnginePool{
        engines:  make(chan *ultrafast.QueryEngine, size),
        indexDir: indexDir,
    }
    
    // Pre-populate pool
    for i := 0; i < size; i++ {
        engine := ultrafast.NewQueryEngine(indexDir)
        pool.engines <- engine
    }
    
    return pool
}

func (p *EnginePool) Get() *ultrafast.QueryEngine {
    return <-p.engines
}

func (p *EnginePool) Put(engine *ultrafast.QueryEngine) {
    select {
    case p.engines <- engine:
    default:
        engine.Close() // Pool full, close engine
    }
}
```

### **Batch Processing**

```go
// Batch index generation
func BatchGenerateIndexes(data map[string][]Record, outputDir string) error {
    generator := ultrafast.NewGenerator(outputDir)
    
    // Process columns in parallel
    var wg sync.WaitGroup
    errChan := make(chan error, len(data))
    
    for column, records := range data {
        wg.Add(1)
        go func(col string, recs []Record) {
            defer wg.Done()
            if err := generator.Generate(col, recs); err != nil {
                errChan <- fmt.Errorf("failed to generate %s: %v", col, err)
            }
        }(column, records)
    }
    
    wg.Wait()
    close(errChan)
    
    // Check for errors
    for err := range errChan {
        return err
    }
    
    return nil
}
```

## 📈 **Monitoring Integration**

### **Metrics Collection**

```go
// Prometheus metrics
var (
    searchDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "ultrafast_search_duration_seconds",
            Help: "Duration of UltraFast search operations",
        },
        []string{"column"},
    )
    
    searchResults = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "ultrafast_search_results_total",
            Help: "Total number of search results",
        },
        []string{"column"},
    )
)

// Instrumented search
func (e *InstrumentedEngine) Search(column, value string) ([]uint32, error) {
    start := time.Now()
    defer func() {
        searchDuration.WithLabelValues(column).Observe(time.Since(start).Seconds())
    }()
    
    results, err := e.engine.Search(column, value)
    if err == nil {
        searchResults.WithLabelValues(column).Add(float64(len(results)))
    }
    
    return results, err
}
```

### **Health Checks**

```go
// Health check endpoint
func (h *HealthHandler) CheckUltraFast(w http.ResponseWriter, r *http.Request) {
    // Test search operation
    start := time.Now()
    _, err := h.engine.Search("test_column", "test_value")
    duration := time.Since(start)
    
    status := "healthy"
    if err != nil || duration > time.Millisecond*100 {
        status = "unhealthy"
        w.WriteHeader(http.StatusServiceUnavailable)
    }
    
    response := map[string]interface{}{
        "status":   status,
        "duration": duration.String(),
        "error":    err,
    }
    
    json.NewEncoder(w).Encode(response)
}
```

## 🔒 **Security Considerations**

### **Input Validation**

```go
func validateSearchInput(column, value string) error {
    // Validate column name
    if !isValidColumnName(column) {
        return fmt.Errorf("invalid column name: %s", column)
    }
    
    // Validate search value
    if len(value) > 1024 {
        return fmt.Errorf("search value too long: %d bytes", len(value))
    }
    
    // Check for injection attempts
    if containsSuspiciousPatterns(value) {
        return fmt.Errorf("suspicious search pattern detected")
    }
    
    return nil
}
```

### **Access Control**

```go
// Role-based access control
type AccessController struct {
    permissions map[string][]string // user -> allowed columns
}

func (ac *AccessController) CanSearch(user, column string) bool {
    allowedColumns, exists := ac.permissions[user]
    if !exists {
        return false
    }
    
    for _, allowed := range allowedColumns {
        if allowed == column || allowed == "*" {
            return true
        }
    }
    
    return false
}
```

## 🚀 **Deployment Strategies**

### **Blue-Green Deployment**

```bash
#!/bin/bash
# Blue-green deployment script

# Generate new indexes (green)
./ultrafast generate data.csv /var/lib/ultrafast/indexes-green/

# Validate new indexes
./ultrafast validate /var/lib/ultrafast/indexes-green/

# Switch traffic to green
ln -sfn /var/lib/ultrafast/indexes-green /var/lib/ultrafast/indexes-current

# Health check
curl -f http://localhost:8080/health/ultrafast

# Clean up old indexes (blue) after validation period
rm -rf /var/lib/ultrafast/indexes-blue/
```

### **Rolling Updates**

```go
// Graceful index updates
func (s *Service) UpdateIndexes(newData map[string][]Record) error {
    tempDir := "/tmp/ultrafast-" + uuid.New().String()
    
    // Generate new indexes in temporary directory
    if err := BatchGenerateIndexes(newData, tempDir); err != nil {
        return err
    }
    
    // Validate new indexes
    if err := s.validateIndexes(tempDir); err != nil {
        os.RemoveAll(tempDir)
        return err
    }
    
    // Atomically replace old indexes
    oldDir := s.indexDir + "-old"
    os.Rename(s.indexDir, oldDir)
    os.Rename(tempDir, s.indexDir)
    
    // Update query engines
    s.reloadEngines()
    
    // Clean up old indexes
    go func() {
        time.Sleep(5 * time.Minute)
        os.RemoveAll(oldDir)
    }()
    
    return nil
}
```

This integration guide provides a comprehensive roadmap for adopting UltraFast Index in production environments while maintaining system reliability and performance.
