// Package ultrafast - Version 2 with optimized file format and vectorized operations
package main

import (
	"encoding/binary"
	"fmt"
	"math"
	"os"
	"sort"
	"syscall"
	"unsafe"
)

// UltraFastV2 file format constants
const (
	V2_MAGIC_NUMBER    = "UFASTV2\x00" // 8 bytes
	V2_HEADER_SIZE     = 128           // Increased for better alignment
	V2_CACHE_LINE_SIZE = 64            // CPU cache line size
	V2_PAGE_SIZE       = 4096          // Memory page size
)

// V2Header represents the optimized file header
type V2Header struct {
	Magic           [8]byte  // "UFASTV2\0"
	Version         uint32   // Format version (2)
	NumKeys         uint32   // Number of unique keys
	HashTableSize   uint32   // Hash table entries
	KeySlotSize     uint32   // Size of each key slot
	DataSectionSize uint64   // Size of data section
	BloomFilterSize uint32   // Size of bloom filter
	CompressionType uint32   // Compression algorithm used
	Checksum        uint32   // Header checksum
	Reserved        [84]byte // Padding to 128 bytes
}

// CompressedKeyEntry represents a compressed key entry
type CompressedKeyEntry struct {
	KeyHash    uint32 // Hash of the key for quick comparison
	KeyLength  uint16 // Length of compressed key
	DataOffset uint32 // Offset to line numbers
	Count      uint32 // Number of line numbers
	Reserved   uint16 // Padding for alignment
}

// BloomFilter for fast negative lookups
type BloomFilter struct {
	bits []uint64
	size uint32
	k    uint32 // Number of hash functions
}

// NewBloomFilter creates a new bloom filter
func NewBloomFilter(expectedElements uint32, falsePositiveRate float64) *BloomFilter {
	// Calculate optimal size and hash functions
	m := uint32(-float64(expectedElements) * math.Log(falsePositiveRate) / (math.Log(2) * math.Log(2)))
	k := uint32(float64(m) / float64(expectedElements) * math.Log(2))

	// Round up to nearest 64-bit boundary
	m = ((m + 63) / 64) * 64

	return &BloomFilter{
		bits: make([]uint64, m/64),
		size: m,
		k:    k,
	}
}

// Add adds an element to the bloom filter
func (bf *BloomFilter) Add(data string) {
	h1 := fastHash32(data)
	h2 := fnvHash32(data)

	for i := uint32(0); i < bf.k; i++ {
		hash := (h1 + i*h2) % bf.size
		wordIndex := hash / 64
		bitIndex := hash % 64
		bf.bits[wordIndex] |= 1 << bitIndex
	}
}

// Contains checks if an element might be in the set
func (bf *BloomFilter) Contains(data string) bool {
	h1 := fastHash32(data)
	h2 := fnvHash32(data)

	for i := uint32(0); i < bf.k; i++ {
		hash := (h1 + i*h2) % bf.size
		wordIndex := hash / 64
		bitIndex := hash % 64
		if (bf.bits[wordIndex] & (1 << bitIndex)) == 0 {
			return false
		}
	}
	return true
}

// V2Generator creates optimized V2 format indexes
type V2Generator struct {
	indexDir string
	config   V2Config
}

// V2Config holds V2 format configuration
type V2Config struct {
	EnableCompression     bool
	EnableBloomFilter     bool
	BloomFilterFPRate     float64
	CacheLineAlignment    bool
	PrefetchOptimization  bool
	UseRoaringCompression bool
	UseSIMDHash           bool
	EnableAdvancedMixing  bool
}

// DefaultV2Config returns default V2 configuration
func DefaultV2Config() V2Config {
	return V2Config{
		EnableCompression:     true,
		EnableBloomFilter:     true,
		BloomFilterFPRate:     0.01, // 1% false positive rate
		CacheLineAlignment:    true,
		PrefetchOptimization:  true,
		UseRoaringCompression: true,
		UseSIMDHash:           true,
		EnableAdvancedMixing:  true,
	}
}

// NewV2Generator creates a new V2 format generator
func NewV2Generator(indexDir string) *V2Generator {
	return &V2Generator{
		indexDir: indexDir,
		config:   DefaultV2Config(),
	}
}

// GenerateV2 creates an optimized V2 format index
func (g *V2Generator) GenerateV2(columnName string, records []Record) error {
	if len(records) == 0 {
		return fmt.Errorf("no records to index")
	}

	// Group records by value
	valueMap := make(map[string][]uint32)
	for _, record := range records {
		valueMap[record.Value] = append(valueMap[record.Value], record.LineNumber)
	}

	// Sort line numbers for each value
	for value := range valueMap {
		sort.Slice(valueMap[value], func(i, j int) bool {
			return valueMap[value][i] < valueMap[value][j]
		})
	}

	// Extract unique keys
	keys := make([]string, 0, len(valueMap))
	for key := range valueMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", g.indexDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create bloom filter if enabled
	var bloomFilter *BloomFilter
	if g.config.EnableBloomFilter {
		bloomFilter = NewBloomFilter(uint32(len(keys)), g.config.BloomFilterFPRate)
		for _, key := range keys {
			bloomFilter.Add(key)
		}
	}

	// Build optimized hash table
	hashTableSize := nextPowerOf2(uint32(len(keys)) * 2)
	hashTable := make([]uint32, hashTableSize)

	for i, key := range keys {
		hash := simdHash32(key) % hashTableSize
		for hashTable[hash] != 0 {
			hash = (hash + 1) % hashTableSize
		}
		hashTable[hash] = uint32(i + 1)
	}

	// Build header
	header := g.buildV2Header(uint32(len(keys)), hashTableSize, bloomFilter)
	if _, err := file.Write((*(*[V2_HEADER_SIZE]byte)(unsafe.Pointer(&header)))[:]); err != nil {
		return err
	}

	// Write bloom filter if enabled
	if bloomFilter != nil {
		bloomBytes := make([]byte, len(bloomFilter.bits)*8)
		for i, word := range bloomFilter.bits {
			binary.LittleEndian.PutUint64(bloomBytes[i*8:(i+1)*8], word)
		}
		if _, err := file.Write(bloomBytes); err != nil {
			return err
		}
	}

	// Write hash table with cache line alignment
	hashTableBytes := make([]byte, hashTableSize*4)
	for i, val := range hashTable {
		binary.LittleEndian.PutUint32(hashTableBytes[i*4:(i+1)*4], val)
	}
	if _, err := file.Write(hashTableBytes); err != nil {
		return err
	}

	// Write compressed key directory
	keyDirectory := make([]CompressedKeyEntry, len(keys))
	var dataSection []byte

	for i, key := range keys {
		lineNumbers := valueMap[key]

		// Compress line numbers using selected compression method
		var compressedData []byte
		if g.config.UseRoaringCompression {
			compressedData = compressLineNumbersRoaring(lineNumbers)
		} else {
			compressedData = g.compressLineNumbers(lineNumbers)
		}

		keyDirectory[i] = CompressedKeyEntry{
			KeyHash:    simdHash32(key),
			KeyLength:  uint16(len(key)),
			DataOffset: uint32(len(dataSection)),
			Count:      uint32(len(lineNumbers)),
			Reserved:   0,
		}

		// Store key length + key + compressed data
		keyData := make([]byte, 2+len(key)+len(compressedData))
		binary.LittleEndian.PutUint16(keyData[0:2], uint16(len(key)))
		copy(keyData[2:2+len(key)], key)
		copy(keyData[2+len(key):], compressedData)

		dataSection = append(dataSection, keyData...)
	}

	// Write key directory
	keyDirBytes := make([]byte, len(keyDirectory)*int(unsafe.Sizeof(CompressedKeyEntry{})))
	for i, entry := range keyDirectory {
		entryBytes := (*(*[unsafe.Sizeof(CompressedKeyEntry{})]byte)(unsafe.Pointer(&entry)))[:]
		copy(keyDirBytes[i*len(entryBytes):(i+1)*len(entryBytes)], entryBytes)
	}
	if _, err := file.Write(keyDirBytes); err != nil {
		return err
	}

	// Write data section
	if _, err := file.Write(dataSection); err != nil {
		return err
	}

	return nil
}

// compressLineNumbers compresses line numbers using delta encoding
func (g *V2Generator) compressLineNumbers(lineNumbers []uint32) []byte {
	if len(lineNumbers) == 0 {
		return []byte{}
	}

	// Use delta encoding for better compression
	compressed := make([]byte, 0, len(lineNumbers)*4)

	// First number is stored as-is
	buf := make([]byte, 4)
	binary.LittleEndian.PutUint32(buf, lineNumbers[0])
	compressed = append(compressed, buf...)

	// Subsequent numbers are stored as deltas
	for i := 1; i < len(lineNumbers); i++ {
		delta := lineNumbers[i] - lineNumbers[i-1]

		// Use variable-length encoding for small deltas
		if delta < 128 {
			compressed = append(compressed, byte(delta))
		} else if delta < 16384 {
			compressed = append(compressed, byte(0x80|(delta>>8)), byte(delta&0xFF))
		} else {
			compressed = append(compressed, 0xFF)
			binary.LittleEndian.PutUint32(buf, delta)
			compressed = append(compressed, buf...)
		}
	}

	return compressed
}

// buildV2Header creates an optimized V2 header
func (g *V2Generator) buildV2Header(numKeys, hashTableSize uint32, bloomFilter *BloomFilter) V2Header {
	header := V2Header{
		Version:         2,
		NumKeys:         numKeys,
		HashTableSize:   hashTableSize,
		KeySlotSize:     uint32(unsafe.Sizeof(CompressedKeyEntry{})),
		DataSectionSize: 0, // Will be calculated
		CompressionType: 1, // Delta encoding
	}

	copy(header.Magic[:], V2_MAGIC_NUMBER)

	if bloomFilter != nil {
		header.BloomFilterSize = uint32(len(bloomFilter.bits) * 8)
	}

	return header
}

// V2QueryEngine provides optimized query execution for V2 format
type V2QueryEngine struct {
	indexDir string
	mmapData map[string][]byte
	config   V2Config
}

// NewV2QueryEngine creates a new V2 query engine
func NewV2QueryEngine(indexDir string) *V2QueryEngine {
	return &V2QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
		config:   DefaultV2Config(),
	}
}

// SearchV2 performs optimized search using V2 format
func (e *V2QueryEngine) SearchV2(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", e.indexDir, columnName)

	// Get memory-mapped data
	data, err := e.getMmapDataV2(filename)
	if err != nil {
		return nil, err
	}

	// Parse header
	header := (*V2Header)(unsafe.Pointer(&data[0]))
	if string(header.Magic[:]) != V2_MAGIC_NUMBER {
		return nil, fmt.Errorf("invalid V2 magic number")
	}

	offset := V2_HEADER_SIZE

	// Check bloom filter first for fast negative lookups
	if header.BloomFilterSize > 0 {
		// Safely read bloom filter data
		bloomSize := int(header.BloomFilterSize / 8)
		bloomBits := make([]uint64, bloomSize)

		for i := 0; i < bloomSize; i++ {
			bloomBits[i] = binary.LittleEndian.Uint64(data[offset+i*8 : offset+(i+1)*8])
		}

		bloomFilter := &BloomFilter{
			bits: bloomBits,
			size: header.BloomFilterSize * 8,
			k:    3, // Assume 3 hash functions
		}

		if !bloomFilter.Contains(searchValue) {
			return []uint32{}, nil // Definitely not present
		}

		offset += int(header.BloomFilterSize)
	}

	// Search hash table
	hashTableSize := header.HashTableSize
	hashTable := make([]uint32, hashTableSize)

	for i := uint32(0); i < hashTableSize; i++ {
		hashTable[i] = binary.LittleEndian.Uint32(data[offset+int(i*4) : offset+int((i+1)*4)])
	}

	offset += int(hashTableSize * 4)

	// Optimized hash lookup
	searchHash := simdHash32(searchValue)
	hash := searchHash % hashTableSize

	// Linear probing with prefetching
	for i := uint32(0); i < hashTableSize; i++ {
		currentHash := (hash + i) % hashTableSize

		// Prefetch next cache line
		if i%16 == 0 && currentHash+16 < hashTableSize {
			prefetchCacheLine(unsafe.Pointer(&hashTable[currentHash+16]))
		}

		keyIndex := hashTable[currentHash]
		if keyIndex == 0 {
			return []uint32{}, nil // Not found
		}

		// Get key entry
		keyDirOffset := offset + int(keyIndex-1)*int(unsafe.Sizeof(CompressedKeyEntry{}))
		keyEntry := (*CompressedKeyEntry)(unsafe.Pointer(&data[keyDirOffset]))

		// Quick hash comparison first
		if keyEntry.KeyHash != searchHash {
			continue
		}

		// Get actual key for verification
		dataOffset := offset + int(header.NumKeys)*int(unsafe.Sizeof(CompressedKeyEntry{})) + int(keyEntry.DataOffset)
		keyLength := binary.LittleEndian.Uint16(data[dataOffset : dataOffset+2])

		if keyLength != uint16(len(searchValue)) {
			continue
		}

		keyData := data[dataOffset+2 : dataOffset+2+int(keyLength)]
		if string(keyData) != searchValue {
			continue
		}

		// Found the key, decompress line numbers
		compressedData := data[dataOffset+2+int(keyLength):]
		if e.config.UseRoaringCompression {
			return decompressLineNumbersRoaring(compressedData), nil
		} else {
			return e.decompressLineNumbers(compressedData, keyEntry.Count), nil
		}
	}

	return []uint32{}, nil // Not found
}

// decompressLineNumbers decompresses delta-encoded line numbers
func (e *V2QueryEngine) decompressLineNumbers(data []byte, count uint32) []uint32 {
	if count == 0 {
		return []uint32{}
	}

	result := make([]uint32, count)

	// First number
	result[0] = binary.LittleEndian.Uint32(data[0:4])
	offset := 4

	// Decompress deltas
	for i := uint32(1); i < count && offset < len(data); i++ {
		if data[offset] < 128 {
			// Single byte delta
			result[i] = result[i-1] + uint32(data[offset])
			offset++
		} else if data[offset] < 0xFF {
			// Two byte delta
			delta := uint32(data[offset]&0x7F)<<8 | uint32(data[offset+1])
			result[i] = result[i-1] + delta
			offset += 2
		} else {
			// Four byte delta
			delta := binary.LittleEndian.Uint32(data[offset+1 : offset+5])
			result[i] = result[i-1] + delta
			offset += 5
		}
	}

	return result
}

// getMmapDataV2 gets memory-mapped data for V2 format
func (e *V2QueryEngine) getMmapDataV2(filename string) ([]byte, error) {
	if data, exists := e.mmapData[filename]; exists {
		return data, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	data, err := syscall.Mmap(int(file.Fd()), 0, int(stat.Size()),
		syscall.PROT_READ, syscall.MAP_SHARED)
	if err != nil {
		return nil, err
	}

	e.mmapData[filename] = data
	return data, nil
}

// prefetchCacheLine hints the CPU to prefetch a cache line
func prefetchCacheLine(addr unsafe.Pointer) {
	// This would use CPU-specific prefetch instructions
	// For now, it's a no-op but could be implemented with assembly
	_ = addr
}

// Advanced SIMD-optimized hash function for V2
func simdHash32(s string) uint32 {
	if len(s) == 0 {
		return 2166136261 // FNV offset basis
	}

	// Use unsafe to avoid string to []byte conversion
	sBytes := *(*[]byte)(unsafe.Pointer(&s))

	var hash uint32 = 2166136261 // FNV offset basis
	const prime uint32 = 16777619

	// Process 16 bytes at a time for maximum SIMD efficiency
	i := 0
	for i+16 <= len(sBytes) {
		// Load 16 bytes as two uint64s
		chunk1 := *(*uint64)(unsafe.Pointer(&sBytes[i]))
		chunk2 := *(*uint64)(unsafe.Pointer(&sBytes[i+8]))

		// Vectorized mixing using bit operations
		hash ^= uint32(chunk1)
		hash *= prime
		hash ^= uint32(chunk1 >> 32)
		hash *= prime
		hash ^= uint32(chunk2)
		hash *= prime
		hash ^= uint32(chunk2 >> 32)
		hash *= prime

		i += 16
	}

	// Process remaining 8 bytes
	if i+8 <= len(sBytes) {
		chunk := *(*uint64)(unsafe.Pointer(&sBytes[i]))
		hash ^= uint32(chunk)
		hash *= prime
		hash ^= uint32(chunk >> 32)
		hash *= prime
		i += 8
	}

	// Process remaining 4 bytes
	if i+4 <= len(sBytes) {
		chunk := *(*uint32)(unsafe.Pointer(&sBytes[i]))
		hash ^= chunk
		hash *= prime
		i += 4
	}

	// Process remaining bytes
	for i < len(sBytes) {
		hash ^= uint32(sBytes[i])
		hash *= prime
		i++
	}

	// Enhanced final mixing for better distribution
	hash ^= hash >> 16
	hash *= 0x85ebca6b
	hash ^= hash >> 13
	hash *= 0xc2b2ae35
	hash ^= hash >> 16

	return hash
}

// RoaringBitmap-style compression for line numbers
func compressLineNumbersRoaring(lineNumbers []uint32) []byte {
	if len(lineNumbers) == 0 {
		return []byte{}
	}

	// Simple run-length encoding for consecutive sequences
	compressed := make([]byte, 0, len(lineNumbers)*2)

	i := 0
	for i < len(lineNumbers) {
		start := lineNumbers[i]
		runLength := 1

		// Find consecutive sequence
		for i+runLength < len(lineNumbers) &&
			lineNumbers[i+runLength] == start+uint32(runLength) {
			runLength++
		}

		if runLength >= 3 {
			// Use run-length encoding for sequences of 3+
			compressed = append(compressed, 0xFF) // Run marker
			compressed = append(compressed, byte(runLength))

			// Store start value (4 bytes)
			startBytes := make([]byte, 4)
			binary.LittleEndian.PutUint32(startBytes, start)
			compressed = append(compressed, startBytes...)
		} else {
			// Store individual values
			for j := 0; j < runLength; j++ {
				valueBytes := make([]byte, 4)
				binary.LittleEndian.PutUint32(valueBytes, lineNumbers[i+j])
				compressed = append(compressed, valueBytes...)
			}
		}

		i += runLength
	}

	return compressed
}

// Decompress roaring-style compressed line numbers
func decompressLineNumbersRoaring(data []byte) []uint32 {
	if len(data) == 0 {
		return []uint32{}
	}

	var result []uint32
	i := 0

	for i < len(data) {
		if data[i] == 0xFF && i+5 < len(data) {
			// Run-length encoded sequence
			runLength := int(data[i+1])
			start := binary.LittleEndian.Uint32(data[i+2 : i+6])

			for j := 0; j < runLength; j++ {
				result = append(result, start+uint32(j))
			}

			i += 6
		} else if i+4 <= len(data) {
			// Individual value
			value := binary.LittleEndian.Uint32(data[i : i+4])
			result = append(result, value)
			i += 4
		} else {
			break
		}
	}

	return result
}

// Close releases memory-mapped files
func (e *V2QueryEngine) Close() error {
	for filename, data := range e.mmapData {
		if err := syscall.Munmap(data); err != nil {
			return fmt.Errorf("failed to unmap %s: %v", filename, err)
		}
	}
	e.mmapData = make(map[string][]byte)
	return nil
}
