package main

import (
	"fmt"
	"os"
	"strings"
	"testing"
	"time"
)

func TestUltraFastIndexBasic(t *testing.T) {
	// Test data
	data := []Record{
		{LineNumber: 1, Value: "test_user_1"},
		{LineNumber: 2, Value: "test_user_2"},
		{LineNumber: 3, Value: "test_user_1"}, // Duplicate
		{LineNumber: 4, Value: "test_user_3"},
	}

	// Create temporary directory
	tempDir := t.TempDir()

	// Test index generation
	generator := NewGenerator(tempDir)
	err := generator.Generate("test_column", data)
	if err != nil {
		t.Fatalf("Failed to generate index: %v", err)
	}

	// Verify index file exists
	indexFile := tempDir + "/test_column_ultrafast.ufidx"
	if _, err := os.Stat(indexFile); os.IsNotExist(err) {
		t.Fatalf("Index file not created: %s", indexFile)
	}

	// Test query engine
	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test search for existing value
	results, err := engine.Search("test_column", "test_user_1")
	if err != nil {
		t.Fatalf("Search failed: %v", err)
	}

	expectedResults := []uint32{1, 3}
	if len(results) != len(expectedResults) {
		t.Fatalf("Expected %d results, got %d", len(expectedResults), len(results))
	}

	for i, expected := range expectedResults {
		if results[i] != expected {
			t.Errorf("Expected result %d to be %d, got %d", i, expected, results[i])
		}
	}

	// Test search for non-existing value
	results, err = engine.Search("test_column", "nonexistent")
	if err != nil {
		t.Fatalf("Search failed: %v", err)
	}

	if len(results) != 0 {
		t.Errorf("Expected 0 results for nonexistent value, got %d", len(results))
	}
}

func TestUltraFastIndexPerformance(t *testing.T) {
	// Generate larger test dataset
	data := make([]Record, 10000)
	for i := 0; i < 10000; i++ {
		data[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      fmt.Sprintf("user_%d", i%1000), // 1000 unique values
		}
	}

	tempDir := t.TempDir()

	// Measure index generation time
	generator := NewGenerator(tempDir)
	start := time.Now()
	err := generator.Generate("perf_test", data)
	generateTime := time.Since(start)

	if err != nil {
		t.Fatalf("Failed to generate index: %v", err)
	}

	t.Logf("Index generation time: %v", generateTime)

	// Test query performance
	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Warm up
	engine.Search("perf_test", "user_0")

	// Measure search time
	searchValue := "user_500"
	start = time.Now()
	results, err := engine.Search("perf_test", searchValue)
	searchTime := time.Since(start)

	if err != nil {
		t.Fatalf("Search failed: %v", err)
	}

	t.Logf("Search time: %v", searchTime)
	t.Logf("Results found: %d", len(results))

	// Performance assertions
	if searchTime > time.Millisecond {
		t.Errorf("Search too slow: %v (expected < 1ms)", searchTime)
	}

	if generateTime > time.Second*5 {
		t.Errorf("Index generation too slow: %v (expected < 5s)", generateTime)
	}
}

func TestUltraFastIndexConcurrency(t *testing.T) {
	// Test data
	data := []Record{
		{LineNumber: 1, Value: "concurrent_test_1"},
		{LineNumber: 2, Value: "concurrent_test_2"},
		{LineNumber: 3, Value: "concurrent_test_3"},
	}

	tempDir := t.TempDir()

	// Generate index
	generator := NewGenerator(tempDir)
	err := generator.Generate("concurrent_test", data)
	if err != nil {
		t.Fatalf("Failed to generate index: %v", err)
	}

	// Test concurrent searches
	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	const numGoroutines = 10
	const searchesPerGoroutine = 100

	errChan := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < searchesPerGoroutine; j++ {
				searchValue := fmt.Sprintf("concurrent_test_%d", (j%3)+1)
				_, err := engine.Search("concurrent_test", searchValue)
				if err != nil {
					errChan <- err
					return
				}
			}
			errChan <- nil
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		if err := <-errChan; err != nil {
			t.Errorf("Concurrent search failed: %v", err)
		}
	}
}

func TestUltraFastIndexEdgeCases(t *testing.T) {
	tempDir := t.TempDir()

	// Test empty data
	generator := NewGenerator(tempDir)
	err := generator.Generate("empty_test", []Record{})
	if err != nil {
		t.Fatalf("Failed to generate index for empty data: %v", err)
	}

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	results, err := engine.Search("empty_test", "anything")
	if err != nil {
		t.Fatalf("Search on empty index failed: %v", err)
	}

	if len(results) != 0 {
		t.Errorf("Expected 0 results from empty index, got %d", len(results))
	}

	// Test single record
	singleData := []Record{{LineNumber: 42, Value: "single_value"}}
	err = generator.Generate("single_test", singleData)
	if err != nil {
		t.Fatalf("Failed to generate index for single record: %v", err)
	}

	results, err = engine.Search("single_test", "single_value")
	if err != nil {
		t.Fatalf("Search on single record index failed: %v", err)
	}

	if len(results) != 1 || results[0] != 42 {
		t.Errorf("Expected [42], got %v", results)
	}

	// Test very long values
	longValue := strings.Repeat("a", 1000)
	longData := []Record{{LineNumber: 1, Value: longValue}}
	err = generator.Generate("long_test", longData)
	if err != nil {
		t.Fatalf("Failed to generate index for long value: %v", err)
	}

	results, err = engine.Search("long_test", longValue)
	if err != nil {
		t.Fatalf("Search for long value failed: %v", err)
	}

	if len(results) != 1 || results[0] != 1 {
		t.Errorf("Expected [1] for long value, got %v", results)
	}
}

func BenchmarkUltraFastSearch(b *testing.B) {
	// Setup
	data := make([]Record, 1000)
	for i := 0; i < 1000; i++ {
		data[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      fmt.Sprintf("benchmark_user_%d", i),
		}
	}

	tempDir := b.TempDir()
	generator := NewGenerator(tempDir)
	generator.Generate("benchmark", data)

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Warm up
	engine.Search("benchmark", "benchmark_user_500")

	b.ResetTimer()

	// Benchmark search operations
	for i := 0; i < b.N; i++ {
		searchValue := fmt.Sprintf("benchmark_user_%d", i%1000)
		_, err := engine.Search("benchmark", searchValue)
		if err != nil {
			b.Fatalf("Search failed: %v", err)
		}
	}
}

func BenchmarkUltraFastGeneration(b *testing.B) {
	data := make([]Record, 1000)
	for i := 0; i < 1000; i++ {
		data[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      fmt.Sprintf("gen_benchmark_user_%d", i),
		}
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		tempDir := b.TempDir()
		generator := NewGenerator(tempDir)
		err := generator.Generate("gen_benchmark", data)
		if err != nil {
			b.Fatalf("Generation failed: %v", err)
		}
	}
}
