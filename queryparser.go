// Package ultrafast - Query Parser and AST
// Provides parsing for multi-column filter expressions with AND/OR logic
package main

import (
	"fmt"
	"strings"
)

// <PERSON><PERSON> represents a lexical token in the query
type Token struct {
	Type  TokenType
	Value string
	Pos   int
}

// TokenType represents the type of token
type TokenType int

const (
	TokenTypeEOF TokenType = iota
	TokenTypeIdentifier
	TokenTypeString
	TokenTypeEquals
	TokenTypeAnd
	TokenTypeOr
	TokenTypeLeftParen
	TokenTypeRightParen
	TokenTypeComma
	TokenTypeSelect
	TokenTypeFrom
	TokenTypeWhere
	TokenTypeLimit
	TokenTypeOffset
)

// Lexer tokenizes query strings
type Lexer struct {
	input  string
	pos    int
	tokens []Token
}

// NewLexer creates a new lexer for the given input
func NewLexer(input string) *Lexer {
	return &Lexer{
		input: strings.TrimSpace(input),
		pos:   0,
	}
}

// Tokenize converts the input string into tokens
func (l *Lexer) Tokenize() ([]Token, error) {
	l.tokens = []Token{}

	for l.pos < len(l.input) {
		// Skip whitespace
		if l.isWhitespace(l.current()) {
			l.advance()
			continue
		}

		// Handle different token types
		switch {
		case l.current() == '(':
			l.addToken(TokenTypeLeftParen, "(")
			l.advance()
		case l.current() == ')':
			l.addToken(TokenTypeRightParen, ")")
			l.advance()
		case l.current() == '=':
			l.addToken(TokenTypeEquals, "=")
			l.advance()
		case l.current() == ',':
			l.addToken(TokenTypeComma, ",")
			l.advance()
		case l.current() == '"' || l.current() == '\'':
			if err := l.readString(); err != nil {
				return nil, err
			}
		case l.isAlpha(l.current()):
			l.readIdentifier()
		default:
			return nil, fmt.Errorf("unexpected character '%c' at position %d", l.current(), l.pos)
		}
	}

	l.addToken(TokenTypeEOF, "")
	return l.tokens, nil
}

// Parser parses tokens into an AST
type Parser struct {
	tokens []Token
	pos    int
}

// NewParser creates a new parser for the given tokens
func NewParser(tokens []Token) *Parser {
	return &Parser{
		tokens: tokens,
		pos:    0,
	}
}

// ParseQuery parses a complete query with SELECT, FROM, WHERE clauses
func (p *Parser) ParseQuery() (*QueryRequest, error) {
	query := &QueryRequest{
		SelectCols: []string{},
		Limit:      0,
		Offset:     0,
	}

	// Parse SELECT clause
	if p.current().Type == TokenTypeSelect {
		p.advance()
		cols, err := p.parseSelectColumns()
		if err != nil {
			return nil, err
		}
		query.SelectCols = cols
	}

	// Parse FROM clause (optional for filter-only queries)
	if p.current().Type == TokenTypeFrom {
		p.advance()
		// Skip table name for now
		if p.current().Type == TokenTypeIdentifier {
			p.advance()
		}
	}

	// Parse WHERE clause
	if p.current().Type == TokenTypeWhere {
		p.advance()
		filter, err := p.parseFilterExpression()
		if err != nil {
			return nil, err
		}
		query.Filter = filter
	} else {
		// If no WHERE, try to parse as direct filter expression
		filter, err := p.parseFilterExpression()
		if err != nil {
			return nil, err
		}
		query.Filter = filter
	}

	// Parse LIMIT clause
	if p.current().Type == TokenTypeLimit {
		p.advance()
		// Implementation for LIMIT parsing would go here
	}

	// Parse OFFSET clause
	if p.current().Type == TokenTypeOffset {
		p.advance()
		// Implementation for OFFSET parsing would go here
	}

	return query, nil
}

// ParseFilterExpression parses a filter expression (for simple filter-only queries)
func (p *Parser) ParseFilterExpression() (*FilterExpression, error) {
	return p.parseFilterExpression()
}

// parseFilterExpression parses OR expressions (lowest precedence)
func (p *Parser) parseFilterExpression() (*FilterExpression, error) {
	left, err := p.parseAndExpression()
	if err != nil {
		return nil, err
	}

	for p.current().Type == TokenTypeOr {
		p.advance()
		right, err := p.parseAndExpression()
		if err != nil {
			return nil, err
		}

		left = &FilterExpression{
			Type:     FilterTypeOr,
			Left:     left,
			Right:    right,
			Operator: LogicalOperatorOr,
		}
	}

	return left, nil
}

// parseAndExpression parses AND expressions (higher precedence than OR)
func (p *Parser) parseAndExpression() (*FilterExpression, error) {
	left, err := p.parsePrimaryExpression()
	if err != nil {
		return nil, err
	}

	for p.current().Type == TokenTypeAnd {
		p.advance()
		right, err := p.parsePrimaryExpression()
		if err != nil {
			return nil, err
		}

		left = &FilterExpression{
			Type:     FilterTypeAnd,
			Left:     left,
			Right:    right,
			Operator: LogicalOperatorAnd,
		}
	}

	return left, nil
}

// parsePrimaryExpression parses primary expressions (column=value or parenthesized expressions)
func (p *Parser) parsePrimaryExpression() (*FilterExpression, error) {
	if p.current().Type == TokenTypeLeftParen {
		p.advance()
		expr, err := p.parseFilterExpression()
		if err != nil {
			return nil, err
		}

		if p.current().Type != TokenTypeRightParen {
			return nil, fmt.Errorf("expected ')' at position %d", p.pos)
		}
		p.advance()

		return expr, nil
	}

	// Parse column=value
	if p.current().Type != TokenTypeIdentifier {
		return nil, fmt.Errorf("expected column name at position %d", p.pos)
	}

	column := p.current().Value
	p.advance()

	if p.current().Type != TokenTypeEquals {
		return nil, fmt.Errorf("expected '=' after column name at position %d", p.pos)
	}
	p.advance()

	if p.current().Type != TokenTypeString && p.current().Type != TokenTypeIdentifier {
		return nil, fmt.Errorf("expected value after '=' at position %d", p.pos)
	}

	value := p.current().Value
	p.advance()

	return &FilterExpression{
		Type:   FilterTypeLeaf,
		Column: column,
		Value:  value,
	}, nil
}

// parseSelectColumns parses the column list in SELECT clause
func (p *Parser) parseSelectColumns() ([]string, error) {
	var columns []string

	if p.current().Type != TokenTypeIdentifier {
		return nil, fmt.Errorf("expected column name in SELECT clause")
	}

	columns = append(columns, p.current().Value)
	p.advance()

	for p.current().Type == TokenTypeComma {
		p.advance()
		if p.current().Type != TokenTypeIdentifier {
			return nil, fmt.Errorf("expected column name after ','")
		}
		columns = append(columns, p.current().Value)
		p.advance()
	}

	return columns, nil
}

// Helper methods for lexer
func (l *Lexer) current() byte {
	if l.pos >= len(l.input) {
		return 0
	}
	return l.input[l.pos]
}

func (l *Lexer) advance() {
	l.pos++
}

func (l *Lexer) isWhitespace(ch byte) bool {
	return ch == ' ' || ch == '\t' || ch == '\n' || ch == '\r'
}

func (l *Lexer) isAlpha(ch byte) bool {
	return (ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || ch == '_'
}

func (l *Lexer) isAlphaNumeric(ch byte) bool {
	return l.isAlpha(ch) || (ch >= '0' && ch <= '9')
}

func (l *Lexer) addToken(tokenType TokenType, value string) {
	l.tokens = append(l.tokens, Token{
		Type:  tokenType,
		Value: value,
		Pos:   l.pos,
	})
}

func (l *Lexer) readString() error {
	quote := l.current()
	l.advance() // Skip opening quote

	start := l.pos
	for l.pos < len(l.input) && l.current() != quote {
		l.advance()
	}

	if l.pos >= len(l.input) {
		return fmt.Errorf("unterminated string at position %d", start)
	}

	value := l.input[start:l.pos]
	l.addToken(TokenTypeString, value)
	l.advance() // Skip closing quote

	return nil
}

func (l *Lexer) readIdentifier() {
	start := l.pos

	for l.pos < len(l.input) && l.isAlphaNumeric(l.current()) {
		l.advance()
	}

	value := l.input[start:l.pos]
	tokenType := l.getKeywordType(value)
	l.addToken(tokenType, value)
}

func (l *Lexer) getKeywordType(value string) TokenType {
	switch strings.ToUpper(value) {
	case "AND":
		return TokenTypeAnd
	case "OR":
		return TokenTypeOr
	case "SELECT":
		return TokenTypeSelect
	case "FROM":
		return TokenTypeFrom
	case "WHERE":
		return TokenTypeWhere
	case "LIMIT":
		return TokenTypeLimit
	case "OFFSET":
		return TokenTypeOffset
	default:
		return TokenTypeIdentifier
	}
}

// Helper methods for parser
func (p *Parser) current() Token {
	if p.pos >= len(p.tokens) {
		return Token{Type: TokenTypeEOF}
	}
	return p.tokens[p.pos]
}

func (p *Parser) advance() {
	if p.pos < len(p.tokens) {
		p.pos++
	}
}

// QueryBuilder provides a fluent interface for building queries
type QueryBuilder struct {
	selectCols []string
	filter     *FilterExpression
	limit      int
	offset     int
}

// NewQueryBuilder creates a new query builder
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		selectCols: []string{},
	}
}

// Select sets the columns to select
func (qb *QueryBuilder) Select(columns ...string) *QueryBuilder {
	qb.selectCols = columns
	return qb
}

// Where sets the filter expression
func (qb *QueryBuilder) Where(filter *FilterExpression) *QueryBuilder {
	qb.filter = filter
	return qb
}

// Limit sets the maximum number of results
func (qb *QueryBuilder) Limit(limit int) *QueryBuilder {
	qb.limit = limit
	return qb
}

// Offset sets the number of results to skip
func (qb *QueryBuilder) Offset(offset int) *QueryBuilder {
	qb.offset = offset
	return qb
}

// Build creates the final QueryRequest
func (qb *QueryBuilder) Build() *QueryRequest {
	return &QueryRequest{
		Filter:     qb.filter,
		SelectCols: qb.selectCols,
		Limit:      qb.limit,
		Offset:     qb.offset,
	}
}

// Helper functions for creating filter expressions
func Eq(column, value string) *FilterExpression {
	return &FilterExpression{
		Type:   FilterTypeLeaf,
		Column: column,
		Value:  value,
	}
}

func And(left, right *FilterExpression) *FilterExpression {
	return &FilterExpression{
		Type:     FilterTypeAnd,
		Left:     left,
		Right:    right,
		Operator: LogicalOperatorAnd,
	}
}

func Or(left, right *FilterExpression) *FilterExpression {
	return &FilterExpression{
		Type:     FilterTypeOr,
		Left:     left,
		Right:    right,
		Operator: LogicalOperatorOr,
	}
}

// ParseQuery is a convenience function that combines lexing and parsing
func ParseQuery(queryString string) (*QueryRequest, error) {
	lexer := NewLexer(queryString)
	tokens, err := lexer.Tokenize()
	if err != nil {
		return nil, fmt.Errorf("lexer error: %v", err)
	}

	parser := NewParser(tokens)
	query, err := parser.ParseQuery()
	if err != nil {
		return nil, fmt.Errorf("parser error: %v", err)
	}

	return query, nil
}

// ParseFilterExpression is a convenience function for parsing filter-only expressions
func ParseFilterExpression(filterString string) (*FilterExpression, error) {
	lexer := NewLexer(filterString)
	tokens, err := lexer.Tokenize()
	if err != nil {
		return nil, fmt.Errorf("lexer error: %v", err)
	}

	parser := NewParser(tokens)
	filter, err := parser.ParseFilterExpression()
	if err != nil {
		return nil, fmt.Errorf("parser error: %v", err)
	}

	return filter, nil
}
