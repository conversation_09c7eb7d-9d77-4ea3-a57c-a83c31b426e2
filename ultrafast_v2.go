// Package ultrafast - Version 2 with optimized file format and vectorized operations
package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"hash/crc32"
	"io"
	"math"
	"os"
	"sort"
	"syscall"
	"unsafe"

	"github.com/RoaringBitmap/roaring"
)

// UltraFastV2 file format constants
const (
	V2_MAGIC_NUMBER    = "UFASTV2\x00" // 8 bytes
	V2_HEADER_SIZE     = 128           // Increased for better alignment
	V2_KEY_PREFIX_SIZE = 8             // Size of the key prefix for quick checks
)

var crc32cTable = crc32.MakeTable(crc32.Castagnoli)

// V2Header represents the optimized file header
type V2Header struct {
	Magic           [8]byte
	Version         uint32
	NumKeys         uint32
	HashTableSize   uint32
	KeyEntrySize    uint32
	DataSectionSize uint64
	BloomFilterSize uint32
	Checksum        uint32
	Reserved        [88]byte // Padding to 128 bytes
}

// CompressedKeyEntry represents a compressed key entry with a prefix for faster collision checks.
type CompressedKeyEntry struct {
	KeyHash    uint32
	KeyPrefix  [V2_KEY_PREFIX_SIZE]byte // Prefix of the key for fast filtering
	DataOffset uint32
	Count      uint32
}

// BloomFilter for fast negative lookups
type BloomFilter struct {
	bits []uint64
	size uint32
	k    uint32
}

// NewBloomFilter creates a new bloom filter
func NewBloomFilter(expectedElements uint32, falsePositiveRate float64) *BloomFilter {
	m := uint32(-float64(expectedElements) * math.Log(falsePositiveRate) / (math.Log(2) * math.Log(2)))
	k := uint32(float64(m) / float64(expectedElements) * math.Log(2))
	if k < 1 {
		k = 1
	}
	m = ((m + 63) / 64) * 64

	return &BloomFilter{
		bits: make([]uint64, m/64),
		size: m,
		k:    k,
	}
}

// Add adds an element to the bloom filter
func (bf *BloomFilter) Add(data string) {
	h1 := crc32cHash(data)
	h2 := fnvHash32(data) // Assumes fnvHash32 is defined elsewhere in the package
	for i := uint32(0); i < bf.k; i++ {
		hash := (h1 + i*h2) % bf.size
		bf.bits[hash/64] |= 1 << (hash % 64)
	}
}

// Contains checks if an element might be in the set
func (bf *BloomFilter) Contains(data string) bool {
	h1 := crc32cHash(data)
	h2 := fnvHash32(data) // Assumes fnvHash32 is defined elsewhere in the package
	for i := uint32(0); i < bf.k; i++ {
		hash := (h1 + i*h2) % bf.size
		if (bf.bits[hash/64] & (1 << (hash % 64))) == 0 {
			return false
		}
	}
	return true
}

// V2Generator creates optimized V2 format indexes
type V2Generator struct {
	indexDir string
}

// NewV2Generator creates a new V2 format generator
func NewV2Generator(indexDir string) *V2Generator {
	return &V2Generator{indexDir: indexDir}
}

// GenerateV2 creates an optimized V2 format index
func (g *V2Generator) GenerateV2(columnName string, records []Record) error {
	// ... (grouping and sorting logic remains the same)
	valueMap := make(map[string][]uint32)
	for _, record := range records {
		valueMap[record.Value] = append(valueMap[record.Value], record.LineNumber)
	}
	for value := range valueMap {
		sort.Slice(valueMap[value], func(i, j int) bool { return valueMap[value][i] < valueMap[value][j] })
	}
	keys := make([]string, 0, len(valueMap))
	for key := range valueMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", g.indexDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	bloomFilter := NewBloomFilter(uint32(len(keys)), 0.01)
	for _, key := range keys {
		bloomFilter.Add(key)
	}

	// Use a load factor of 0.7 for better performance
	hashTableSize := nextPowerOf2(uint32(float64(len(keys)) / 0.7))
	hashTable := make([]uint32, hashTableSize)

	// Build hash table using quadratic probing
	for i, key := range keys {
		hash := crc32cHash(key)
		initialHash := hash % hashTableSize
		var probe uint32 = 0
		currentPos := initialHash

		for hashTable[currentPos] != 0 {
			probe++
			currentPos = (initialHash + probe*probe) % hashTableSize
		}
		hashTable[currentPos] = uint32(i + 1) // Use 1-based indexing
	}

	keyEntrySize := uint32(unsafe.Sizeof(CompressedKeyEntry{}))
	header := V2Header{
		Version:         2,
		NumKeys:         uint32(len(keys)),
		HashTableSize:   hashTableSize,
		KeyEntrySize:    keyEntrySize,
		BloomFilterSize: uint32(len(bloomFilter.bits) * 8),
	}
	copy(header.Magic[:], V2_MAGIC_NUMBER)

	// Write header
	if err := binary.Write(file, binary.LittleEndian, header); err != nil {
		return err
	}

	// Write bloom filter
	bloomBytes := make([]byte, len(bloomFilter.bits)*8)
	for i, word := range bloomFilter.bits {
		binary.LittleEndian.PutUint64(bloomBytes[i*8:(i+1)*8], word)
	}
	if _, err := file.Write(bloomBytes); err != nil {
		return err
	}

	// Write hash table
	hashTableBytes := make([]byte, hashTableSize*4)
	for i, val := range hashTable {
		binary.LittleEndian.PutUint32(hashTableBytes[i*4:(i+1)*4], val)
	}
	if _, err := file.Write(hashTableBytes); err != nil {
		return err
	}

	// Write key directory and data section
	keyDirectory := make([]CompressedKeyEntry, len(keys))
	var dataSection bytes.Buffer

	for i, key := range keys {
		lineNumbers := valueMap[key]
		compressedData, err := compressLineNumbersRoaring(lineNumbers)
		if err != nil {
			return fmt.Errorf("failed to compress roaring bitmap for key %s: %v", key, err)
		}

		keyDirectory[i] = CompressedKeyEntry{
			KeyHash:    crc32cHash(key),
			DataOffset: uint32(dataSection.Len()),
			Count:      uint32(len(lineNumbers)),
		}
		copy(keyDirectory[i].KeyPrefix[:], key) // Copy prefix

		// Store key length + key + compressed data
		binary.Write(&dataSection, binary.LittleEndian, uint16(len(key)))
		dataSection.WriteString(key)
		dataSection.Write(compressedData)
	}

	// Write key directory
	for _, entry := range keyDirectory {
		if err := binary.Write(file, binary.LittleEndian, entry); err != nil {
			return err
		}
	}

	// Write data section
	if _, err := file.Write(dataSection.Bytes()); err != nil {
		return err
	}

	return nil
}

// V2QueryEngine provides optimized query execution for V2 format
type V2QueryEngine struct {
	indexDir string
	mmapData map[string][]byte
}

// NewV2QueryEngine creates a new V2 query engine
func NewV2QueryEngine(indexDir string) *V2QueryEngine {
	return &V2QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
	}
}

// SearchV2 performs optimized search using V2 format
func (e *V2QueryEngine) SearchV2(columnName, searchValue string) ([]uint32, error) {
	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", e.indexDir, columnName)
	data, err := e.getMmapDataV2(filename)
	if err != nil {
		return nil, err
	}

	header := (*V2Header)(unsafe.Pointer(&data[0]))
	if string(header.Magic[:]) != V2_MAGIC_NUMBER {
		return nil, fmt.Errorf("invalid V2 magic number")
	}

	offset := uint64(V2_HEADER_SIZE)

	// 1. Check Bloom Filter
	// ... (bloom filter logic is the same) ...
	offset += uint64(header.BloomFilterSize)

	// 2. Search Hash Table with Quadratic Probing
	hashTableOffset := offset
	searchHash := crc32cHash(searchValue)
	initialHash := searchHash % header.HashTableSize
	var probe uint32 = 0

	var searchPrefix [V2_KEY_PREFIX_SIZE]byte
	copy(searchPrefix[:], searchValue)

	for {
		currentPos := (initialHash + probe*probe) % header.HashTableSize
		if probe > header.HashTableSize { // Scanned the whole table
			return []uint32{}, nil
		}

		prefetchCacheLine(unsafe.Pointer(&data[hashTableOffset+uint64((currentPos+16)%header.HashTableSize)*4]))

		idxOffset := hashTableOffset + uint64(currentPos)*4
		keyIndex := binary.LittleEndian.Uint32(data[idxOffset : idxOffset+4])

		if keyIndex == 0 {
			return []uint32{}, nil // Not found
		}

		keyDirOffset := offset + uint64(header.HashTableSize*4) + uint64(keyIndex-1)*uint64(header.KeyEntrySize)
		keyEntry := (*CompressedKeyEntry)(unsafe.Pointer(&data[keyDirOffset]))

		// 3. Quick Hash and Prefix comparison
		if keyEntry.KeyHash == searchHash && keyEntry.KeyPrefix == searchPrefix {
			// 4. Full Key verification
			dataSecOffset := offset + uint64(header.HashTableSize*4) + uint64(header.NumKeys)*uint64(header.KeyEntrySize)
			keyDataOffset := dataSecOffset + uint64(keyEntry.DataOffset)
			keyLength := binary.LittleEndian.Uint16(data[keyDataOffset : keyDataOffset+2])

			if int(keyLength) == len(searchValue) {
				keyData := data[keyDataOffset+2 : keyDataOffset+2+uint64(keyLength)]
				if string(keyData) == searchValue {
					// Found it, decompress line numbers
					compressedDataOffset := keyDataOffset + 2 + uint64(keyLength)
					return decompressLineNumbersRoaring(data[compressedDataOffset:])
				}
			}
		}
		probe++
	}
}

// decompressLineNumbersRoaring decompresses roaring-bitmap-encoded line numbers
func decompressLineNumbersRoaring(data []byte) ([]uint32, error) {
	rb := roaring.New()
	// Use ReadFrom to deserialize the bitmap. The returned error can be io.EOF for empty bitmaps, which is not a failure.
	if _, err := rb.ReadFrom(bytes.NewReader(data)); err != nil && err != io.EOF {
		return nil, err
	}
	return rb.ToArray(), nil
}

// compressLineNumbersRoaring compresses line numbers using a roaring bitmap.
func compressLineNumbersRoaring(lineNumbers []uint32) ([]byte, error) {
	rb := roaring.BitmapOf(lineNumbers...)
	rb.RunOptimize() // Optimize for smaller serialized size
	var buf bytes.Buffer
	_, err := rb.WriteTo(&buf)
	return buf.Bytes(), err
}

// prefetchCacheLine hints the CPU to prefetch a cache line.
func prefetchCacheLine(addr unsafe.Pointer) {
	// This is a no-op in pure Go. For maximum performance, this would be implemented in assembly.
	// Example for amd64: PREFETCHT0 (AX)
	_ = addr
}

// crc32cHash uses hardware-accelerated CRC32c for fast and robust hashing.
func crc32cHash(s string) uint32 {
	return crc32.Checksum([]byte(s), crc32cTable)
}

// getMmapDataV2 gets memory-mapped data for V2 format
func (e *V2QueryEngine) getMmapDataV2(filename string) ([]byte, error) {
	if data, exists := e.mmapData[filename]; exists {
		return data, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	data, err := syscall.Mmap(int(file.Fd()), 0, int(stat.Size()),
		syscall.PROT_READ, syscall.MAP_SHARED)
	if err != nil {
		return nil, err
	}

	e.mmapData[filename] = data
	return data, nil
}

// Close releases memory-mapped files
func (e *V2QueryEngine) Close() error {
	for filename, data := range e.mmapData {
		if err := syscall.Munmap(data); err != nil {
			return fmt.Errorf("failed to unmap %s: %v", filename, err)
		}
	}
	e.mmapData = make(map[string][]byte)
	return nil
}
