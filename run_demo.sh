#!/bin/bash

# Simple demo runner script
echo "🚀 UltraFast Index Demo Runner"
echo "=============================="
echo ""

# Check if mock_data.csv exists
if [ ! -f "mock_data.csv" ]; then
    echo "❌ Error: mock_data.csv not found in current directory"
    echo "Please ensure the data file is present and try again."
    exit 1
fi

echo "✅ Data file found: mock_data.csv"
echo ""

echo "Choose demo type:"
echo "1) 🚀 Comprehensive Demo (all 4 implementations)"
echo "2) ⚡ Quick Demo (2 implementations)"
echo "3) 📋 Show available commands"
echo ""
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🚀 Running comprehensive performance comparison..."
        echo "This will test multiple implementations and may take 5-10 minutes."
        echo ""
        # Run with specific file to avoid main function conflicts
        go run demo_performance_comparison_standalone.go
        ;;
    2)
        echo ""
        echo "⚡ Running quick performance comparison..."
        echo "This will test 2 implementations and takes about 2 minutes."
        echo ""
        # Run with specific file to avoid main function conflicts
        go run simple_demo_standalone.go
        ;;
    3)
        echo ""
        echo "📋 Available commands:"
        echo ""
        echo "UltraFast Index Commands:"
        echo "  go run main.go ultrafast.go ultrafast_v2.go queryparser.go rowstore.go help"
        echo ""
        echo "Existing Implementation Commands:"
        echo "  cd existing_standalone && go run . help"
        echo ""
        echo "Demo Commands:"
        echo "  go run simple_demo_standalone.go"
        echo "  go run demo_performance_comparison_standalone.go"
        ;;
    *)
        echo "Invalid choice. Please run the script again and choose 1, 2, or 3."
        exit 1
        ;;
esac

echo ""
echo "✅ Demo completed!"
