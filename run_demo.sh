#!/bin/bash

# Performance comparison demo runner script
echo "🚀 UltraFast Index Performance Comparison Demo"
echo "=============================================="
echo ""

# Check if mock_data.csv exists
if [ ! -f "mock_data.csv" ]; then
    echo "❌ Error: mock_data.csv not found in current directory"
    echo "Please ensure the data file is present and try again."
    exit 1
fi

echo "✅ Data file found: mock_data.csv"
echo ""

echo "Choose demo type:"
echo "1) 🚀 Comprehensive Demo (Ultrafast, Existing, ExistingInx - All 4 implementations)"
echo "2) ⚡ Quick Demo (Ultrafast vs Existing - 2 implementations)"
echo "3) 📊 Three-Way Comparison (Ultrafast, Existing, ExistingInx - 3 implementations)"
echo "4) 📋 Show available commands"
echo ""
read -p "Enter choice (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🚀 Running comprehensive performance comparison..."
        echo "This will test all 4 implementations: Existing, ExistingInx, UltraFast V1, UltraFast V2"
        echo "Expected duration: 5-10 minutes"
        echo ""
        # Run comprehensive demo with all 4 implementations
        go run demo/demo_performance_comparison.go
        ;;
    2)
        echo ""
        echo "⚡ Running quick performance comparison..."
        echo "This will test 2 implementations: Existing vs UltraFast V1"
        echo "Expected duration: 2-3 minutes"
        echo ""
        # Run quick demo with 2 implementations
        go run simple_demo_standalone.go
        ;;
    3)
        echo ""
        echo "📊 Running three-way performance comparison..."
        echo "This will test 3 implementations: Existing, ExistingInx, UltraFast V1"
        echo "Expected duration: 3-5 minutes"
        echo ""
        # Run three-way comparison
        go run demo_performance_comparison_standalone.go
        ;;
    4)
        echo ""
        echo "📋 Available commands:"
        echo ""
        echo "UltraFast Index Commands:"
        echo "  go run . help"
        echo "  go run . generate-full mock_data.csv ./demo_indexes demo_table"
        echo "  go run . query ./demo_indexes demo_table \"protocol=TCP\" protocol,action"
        echo ""
        echo "Existing Implementation Commands:"
        echo "  cd existing_standalone && go run . help"
        echo "  cd existing_standalone && go run . generate ../mock_data.csv ../demo_existing"
        echo "  cd existing_standalone && go run . generate-compressed ../mock_data.csv ../demo_existing_inx"
        echo ""
        echo "Demo Commands:"
        echo "  go run simple_demo_standalone.go                    # Quick 2-way comparison"
        echo "  go run demo_performance_comparison_standalone.go    # 3-way comparison"
        echo "  go run demo/demo_performance_comparison.go          # Full 4-way comparison"
        echo ""
        echo "Performance Testing:"
        echo "  ./mock_data_demo.sh                                 # Advanced query demos"
        echo "  go run . benchmark ./demo_indexes queries.txt      # Custom benchmark"
        ;;
    *)
        echo "Invalid choice. Please run the script again and choose 1-4."
        exit 1
        ;;
esac

echo ""
echo "✅ Demo completed!"
