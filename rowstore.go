// Package ultrafast - Row Store Component
// Provides efficient storage and retrieval of complete row data
package main

import (
	"encoding/binary"
	"fmt"
	"hash/crc32"
	"os"
	"sort"
	"syscall"
)

// RowStore manages storage and retrieval of complete row data
type RowStore struct {
	indexDir string
	mmapData map[string][]byte
	config   Config
}

// RowStoreHeader represents the header of a .ufrow file
type RowStoreHeader struct {
	Magic        [8]byte  // "UFROWSTO"
	Version      uint32   // File format version
	NumRows      uint32   // Total number of rows
	NumColumns   uint32   // Number of columns
	HeaderSize   uint32   // Size of header (128 bytes)
	RowIndexSize uint32   // Size of row index section
	DataSize     uint32   // Size of data section
	MaxRowSize   uint32   // Maximum row size in bytes
	Checksum     uint32   // Header checksum
	Reserved     [92]byte // Reserved for future use
}

// RowIndexEntry represents an entry in the row index
type RowIndexEntry struct {
	LineNumber uint32 // Original line number from CSV
	Offset     uint32 // Offset to row data in data section
	Size       uint32 // Size of row data in bytes
	Reserved   uint32 // Reserved for future use
}

// NewRowStore creates a new row store instance
func NewRowStore(indexDir string) *RowStore {
	return &RowStore{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
		config:   DefaultConfig(),
	}
}

// GenerateRowStore creates a .ufrow file from CSV data
func (rs *RowStore) GenerateRowStore(tableName string, rows []RowRecord) error {
	if len(rows) == 0 {
		return fmt.Errorf("no rows to store")
	}

	// Sort rows by line number for efficient access
	sort.Slice(rows, func(i, j int) bool {
		return rows[i].LineNumber < rows[j].LineNumber
	})

	// Extract column names from first row
	var columns []string
	for col := range rows[0].Values {
		columns = append(columns, col)
	}
	sort.Strings(columns) // Consistent column ordering

	filename := fmt.Sprintf("%s/%s.ufrow", rs.indexDir, tableName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Calculate sizes and build row index
	rowIndex := make([]RowIndexEntry, len(rows))
	var dataSection []byte
	maxRowSize := uint32(0)

	for i, row := range rows {
		// Serialize row data
		rowData := rs.serializeRow(row, columns)

		rowIndex[i] = RowIndexEntry{
			LineNumber: row.LineNumber,
			Offset:     uint32(len(dataSection)),
			Size:       uint32(len(rowData)),
			Reserved:   0,
		}

		if uint32(len(rowData)) > maxRowSize {
			maxRowSize = uint32(len(rowData))
		}

		dataSection = append(dataSection, rowData...)
	}

	// Write header
	header := rs.buildRowStoreHeader(uint32(len(rows)), uint32(len(columns)),
		uint32(len(rowIndex)*16), uint32(len(dataSection)), maxRowSize)
	if _, err := file.Write(header[:]); err != nil {
		return err
	}

	// Write column names directory
	columnDir := rs.buildColumnDirectory(columns)
	if _, err := file.Write(columnDir); err != nil {
		return err
	}

	// Write row index
	for _, entry := range rowIndex {
		entryBytes := make([]byte, 16)
		binary.BigEndian.PutUint32(entryBytes[0:4], entry.LineNumber)
		binary.BigEndian.PutUint32(entryBytes[4:8], entry.Offset)
		binary.BigEndian.PutUint32(entryBytes[8:12], entry.Size)
		binary.BigEndian.PutUint32(entryBytes[12:16], entry.Reserved)

		if _, err := file.Write(entryBytes); err != nil {
			return err
		}
	}

	// Write data section
	if _, err := file.Write(dataSection); err != nil {
		return err
	}

	return nil
}

// GetRow retrieves a complete row by line number
func (rs *RowStore) GetRow(tableName string, lineNumber uint32) (map[string]string, error) {
	filename := fmt.Sprintf("%s/%s.ufrow", rs.indexDir, tableName)

	data, err := rs.getMmapData(filename)
	if err != nil {
		return nil, err
	}

	// Parse header
	if len(data) < 128 {
		return nil, fmt.Errorf("invalid row store file: too small")
	}

	numRows := binary.BigEndian.Uint32(data[12:16])
	numColumns := binary.BigEndian.Uint32(data[16:20])
	headerSize := binary.BigEndian.Uint32(data[20:24])

	// Read column names
	columns, err := rs.parseColumnDirectory(data, headerSize, numColumns)
	if err != nil {
		return nil, err
	}

	// Binary search in row index
	rowIndexOffset := int(headerSize) + len(columns)*32 // 32 bytes per column name
	rowIndex := rs.findRowIndex(data, rowIndexOffset, numRows, lineNumber)
	if rowIndex == nil {
		return nil, fmt.Errorf("row with line number %d not found", lineNumber)
	}

	// Read row data
	dataOffset := rowIndexOffset + int(numRows)*16 + int(rowIndex.Offset)
	if dataOffset+int(rowIndex.Size) > len(data) {
		return nil, fmt.Errorf("data corruption: invalid row offset")
	}

	rowData := data[dataOffset : dataOffset+int(rowIndex.Size)]
	return rs.deserializeRow(rowData, columns)
}

// GetRows retrieves multiple rows by line numbers
func (rs *RowStore) GetRows(tableName string, lineNumbers []uint32) ([]map[string]string, error) {
	results := make([]map[string]string, 0, len(lineNumbers))

	for _, lineNum := range lineNumbers {
		row, err := rs.GetRow(tableName, lineNum)
		if err != nil {
			// Skip missing rows but continue processing
			continue
		}
		results = append(results, row)
	}

	return results, nil
}

// GetRowsWithProjection retrieves multiple rows with only specified columns
func (rs *RowStore) GetRowsWithProjection(tableName string, lineNumbers []uint32, selectCols []string) ([]map[string]string, error) {
	rows, err := rs.GetRows(tableName, lineNumbers)
	if err != nil {
		return nil, err
	}

	// Project only requested columns
	results := make([]map[string]string, 0, len(rows))
	for _, row := range rows {
		projectedRow := make(map[string]string)
		for _, col := range selectCols {
			if value, exists := row[col]; exists {
				projectedRow[col] = value
			}
		}
		results = append(results, projectedRow)
	}

	return results, nil
}

// buildRowStoreHeader creates the 128-byte header
func (rs *RowStore) buildRowStoreHeader(numRows, numColumns, rowIndexSize, dataSize, maxRowSize uint32) [128]byte {
	var header [128]byte

	copy(header[0:8], []byte("UFROWSTO"))
	binary.BigEndian.PutUint32(header[8:12], rs.config.Version)
	binary.BigEndian.PutUint32(header[12:16], numRows)
	binary.BigEndian.PutUint32(header[16:20], numColumns)
	binary.BigEndian.PutUint32(header[20:24], 128) // Header size
	binary.BigEndian.PutUint32(header[24:28], rowIndexSize)
	binary.BigEndian.PutUint32(header[28:32], dataSize)
	binary.BigEndian.PutUint32(header[32:36], maxRowSize)

	// Calculate checksum (excluding checksum field and reserved area)
	checksum := crc32.ChecksumIEEE(header[0:36])
	binary.BigEndian.PutUint32(header[36:40], checksum)

	return header
}

// buildColumnDirectory creates the column names directory
func (rs *RowStore) buildColumnDirectory(columns []string) []byte {
	// Each column name gets 32 bytes (null-terminated)
	columnDir := make([]byte, len(columns)*32)

	for i, col := range columns {
		offset := i * 32
		copy(columnDir[offset:offset+32], col)
		// Null termination is automatic due to zero initialization
	}

	return columnDir
}

// serializeRow converts a row to binary format
func (rs *RowStore) serializeRow(row RowRecord, columns []string) []byte {
	var data []byte

	// Write number of columns
	colCountBytes := make([]byte, 4)
	binary.BigEndian.PutUint32(colCountBytes, uint32(len(columns)))
	data = append(data, colCountBytes...)

	// Write each column value with length prefix
	for _, col := range columns {
		value := row.Values[col]

		// Write value length
		lengthBytes := make([]byte, 4)
		binary.BigEndian.PutUint32(lengthBytes, uint32(len(value)))
		data = append(data, lengthBytes...)

		// Write value data
		data = append(data, []byte(value)...)
	}

	return data
}

// deserializeRow converts binary data back to a row map
func (rs *RowStore) deserializeRow(data []byte, columns []string) (map[string]string, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("invalid row data: too small")
	}

	result := make(map[string]string)
	offset := 0

	// Read number of columns
	numCols := binary.BigEndian.Uint32(data[offset : offset+4])
	offset += 4

	if int(numCols) != len(columns) {
		return nil, fmt.Errorf("column count mismatch: expected %d, got %d", len(columns), numCols)
	}

	// Read each column value
	for i, col := range columns {
		if offset+4 > len(data) {
			return nil, fmt.Errorf("data corruption: insufficient data for column %d", i)
		}

		// Read value length
		valueLen := binary.BigEndian.Uint32(data[offset : offset+4])
		offset += 4

		if offset+int(valueLen) > len(data) {
			return nil, fmt.Errorf("data corruption: insufficient data for value of column %s", col)
		}

		// Read value data
		value := string(data[offset : offset+int(valueLen)])
		offset += int(valueLen)

		result[col] = value
	}

	return result, nil
}

// parseColumnDirectory reads column names from the file
func (rs *RowStore) parseColumnDirectory(data []byte, headerSize, numColumns uint32) ([]string, error) {
	offset := int(headerSize)
	columns := make([]string, numColumns)

	for i := uint32(0); i < numColumns; i++ {
		if offset+32 > len(data) {
			return nil, fmt.Errorf("data corruption: insufficient data for column directory")
		}

		// Read null-terminated string
		colData := data[offset : offset+32]
		nameLen := 0
		for j, b := range colData {
			if b == 0 {
				nameLen = j
				break
			}
		}

		columns[i] = string(colData[:nameLen])
		offset += 32
	}

	return columns, nil
}

// findRowIndex performs binary search to find row index entry
func (rs *RowStore) findRowIndex(data []byte, indexOffset int, numRows, lineNumber uint32) *RowIndexEntry {
	left, right := 0, int(numRows)-1

	for left <= right {
		mid := (left + right) / 2
		entryOffset := indexOffset + mid*16

		if entryOffset+16 > len(data) {
			return nil
		}

		entryLineNum := binary.BigEndian.Uint32(data[entryOffset : entryOffset+4])

		if entryLineNum == lineNumber {
			return &RowIndexEntry{
				LineNumber: binary.BigEndian.Uint32(data[entryOffset : entryOffset+4]),
				Offset:     binary.BigEndian.Uint32(data[entryOffset+4 : entryOffset+8]),
				Size:       binary.BigEndian.Uint32(data[entryOffset+8 : entryOffset+12]),
				Reserved:   binary.BigEndian.Uint32(data[entryOffset+12 : entryOffset+16]),
			}
		} else if entryLineNum < lineNumber {
			left = mid + 1
		} else {
			right = mid - 1
		}
	}

	return nil
}

// getMmapData returns memory-mapped data for the file
func (rs *RowStore) getMmapData(filename string) ([]byte, error) {
	if data, exists := rs.mmapData[filename]; exists {
		return data, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	data, err := syscall.Mmap(int(file.Fd()), 0, int(stat.Size()),
		syscall.PROT_READ, syscall.MAP_SHARED)
	if err != nil {
		return nil, err
	}

	rs.mmapData[filename] = data
	return data, nil
}

// GetRowCount returns the total number of rows in the table
func (rs *RowStore) GetRowCount(tableName string) (uint32, error) {
	filename := fmt.Sprintf("%s/%s.ufrow", rs.indexDir, tableName)

	data, err := rs.getMmapData(filename)
	if err != nil {
		return 0, err
	}

	if len(data) < 128 {
		return 0, fmt.Errorf("invalid row store file: too small")
	}

	numRows := binary.BigEndian.Uint32(data[12:16])
	return numRows, nil
}

// GetColumnNames returns all column names for the table
func (rs *RowStore) GetColumnNames(tableName string) ([]string, error) {
	filename := fmt.Sprintf("%s/%s.ufrow", rs.indexDir, tableName)

	data, err := rs.getMmapData(filename)
	if err != nil {
		return nil, err
	}

	if len(data) < 128 {
		return nil, fmt.Errorf("invalid row store file: too small")
	}

	numColumns := binary.BigEndian.Uint32(data[16:20])
	headerSize := binary.BigEndian.Uint32(data[20:24])

	return rs.parseColumnDirectory(data, headerSize, numColumns)
}

// GetRowRange retrieves a range of rows by line numbers (inclusive)
func (rs *RowStore) GetRowRange(tableName string, startLine, endLine uint32, selectCols []string) ([]map[string]string, error) {
	var lineNumbers []uint32
	for line := startLine; line <= endLine; line++ {
		lineNumbers = append(lineNumbers, line)
	}

	if len(selectCols) > 0 {
		return rs.GetRowsWithProjection(tableName, lineNumbers, selectCols)
	}
	return rs.GetRows(tableName, lineNumbers)
}

// GetSampleRows returns a sample of rows for data exploration
func (rs *RowStore) GetSampleRows(tableName string, sampleSize int, selectCols []string) ([]map[string]string, error) {
	totalRows, err := rs.GetRowCount(tableName)
	if err != nil {
		return nil, err
	}

	if sampleSize <= 0 || sampleSize > int(totalRows) {
		sampleSize = int(totalRows)
	}

	// Simple sampling: take every nth row
	step := int(totalRows) / sampleSize
	if step < 1 {
		step = 1
	}

	var lineNumbers []uint32
	for i := 1; i <= int(totalRows) && len(lineNumbers) < sampleSize; i += step {
		lineNumbers = append(lineNumbers, uint32(i))
	}

	if len(selectCols) > 0 {
		return rs.GetRowsWithProjection(tableName, lineNumbers, selectCols)
	}
	return rs.GetRows(tableName, lineNumbers)
}

// ValidateRowStore performs integrity checks on the row store file
func (rs *RowStore) ValidateRowStore(tableName string) error {
	filename := fmt.Sprintf("%s/%s.ufrow", rs.indexDir, tableName)

	data, err := rs.getMmapData(filename)
	if err != nil {
		return err
	}

	if len(data) < 128 {
		return fmt.Errorf("invalid row store file: too small")
	}

	// Validate magic number
	if string(data[0:8]) != "UFROWSTO" {
		return fmt.Errorf("invalid magic number")
	}

	// Validate header checksum
	expectedChecksum := binary.BigEndian.Uint32(data[36:40])
	actualChecksum := crc32.ChecksumIEEE(data[0:36])
	if expectedChecksum != actualChecksum {
		return fmt.Errorf("header checksum mismatch")
	}

	numRows := binary.BigEndian.Uint32(data[12:16])
	numColumns := binary.BigEndian.Uint32(data[16:20])
	headerSize := binary.BigEndian.Uint32(data[20:24])

	// Validate that we can read column directory
	_, err = rs.parseColumnDirectory(data, headerSize, numColumns)
	if err != nil {
		return fmt.Errorf("invalid column directory: %v", err)
	}

	// Validate row index accessibility
	rowIndexOffset := int(headerSize) + int(numColumns)*32
	if rowIndexOffset+int(numRows)*16 > len(data) {
		return fmt.Errorf("row index extends beyond file")
	}

	return nil
}

// GetTableStats returns statistics about the table
func (rs *RowStore) GetTableStats(tableName string) (map[string]interface{}, error) {
	filename := fmt.Sprintf("%s/%s.ufrow", rs.indexDir, tableName)

	// Get file info
	stat, err := os.Stat(filename)
	if err != nil {
		return nil, err
	}

	data, err := rs.getMmapData(filename)
	if err != nil {
		return nil, err
	}

	if len(data) < 128 {
		return nil, fmt.Errorf("invalid row store file: too small")
	}

	numRows := binary.BigEndian.Uint32(data[12:16])
	numColumns := binary.BigEndian.Uint32(data[16:20])
	maxRowSize := binary.BigEndian.Uint32(data[32:36])

	columns, err := rs.GetColumnNames(tableName)
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"file_size":     stat.Size(),
		"file_size_mb":  float64(stat.Size()) / (1024 * 1024),
		"num_rows":      numRows,
		"num_columns":   numColumns,
		"max_row_size":  maxRowSize,
		"columns":       columns,
		"last_modified": stat.ModTime(),
	}

	return stats, nil
}

// Close releases memory-mapped files
func (rs *RowStore) Close() error {
	for filename, data := range rs.mmapData {
		if err := syscall.Munmap(data); err != nil {
			return fmt.Errorf("failed to unmap %s: %v", filename, err)
		}
	}
	rs.mmapData = make(map[string][]byte)
	return nil
}
