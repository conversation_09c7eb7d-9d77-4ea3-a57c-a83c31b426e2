package main

import (
	"fmt"
	"os"
	"testing"
)

// TestMockDataIntegration tests the complete workflow with mock_data.csv
func TestMockDataIntegration(t *testing.T) {
	// Check if mock_data.csv exists
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		t.<PERSON>("mock_data.csv not found, skipping integration test")
	}

	// Create temporary directory for indexes
	tempDir, err := os.MkdirTemp("", "ultrafast_mock_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	t.Logf("Using temp directory: %s", tempDir)

	// Read mock data
	rows, columns, err := readFullCSV("mock_data.csv")
	if err != nil {
		t.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	t.Logf("Read %d rows with %d columns", len(rows), len(columns))
	t.Logf("Columns: %v", columns)

	// Generate full indexes
	generator := NewGenerator(tempDir)

	// Generate indexes for key columns (protocol, rule_name, and a few others)
	keyColumns := []string{"protocol", "rule_name", "action", "rule_category", "source_country", "destination_country"}

	for _, column := range keyColumns {
		columnData := extractColumnData(rows, column)
		if len(columnData) == 0 {
			t.Logf("Warning: No data found for column %s", column)
			continue
		}

		if err := generator.Generate(column, columnData); err != nil {
			t.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
		t.Logf("Generated index for column: %s (%d records)", column, len(columnData))
	}

	// Generate row store
	rowStore := NewRowStore(tempDir)
	if err := rowStore.GenerateRowStore("network_logs", rows); err != nil {
		t.Fatalf("Failed to generate row store: %v", err)
	}

	t.Logf("Generated row store with %d rows", len(rows))

	// Test query engine
	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test 1: Simple protocol filter
	t.Run("ProtocolFilter", func(t *testing.T) {
		filter := Eq("protocol", "TCP")
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"timestamp", "source_ip", "destination_ip", "action", "rule_name"},
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		t.Logf("TCP protocol filter: Found %d matches in %v", result.TotalMatches, result.QueryTime)

		if result.TotalMatches == 0 {
			t.Error("Expected some TCP records")
		}

		// Verify all results have TCP protocol
		for i, row := range result.Rows {
			if i >= 5 { // Check first 5 rows
				break
			}
			t.Logf("Row %d: %s -> %s (%s) [%s]", i+1, row["source_ip"], row["destination_ip"], row["action"], row["rule_name"])
		}
	})

	// Test 2: Rule name filter
	t.Run("RuleNameFilter", func(t *testing.T) {
		// Get a sample rule name from the data
		sampleRuleName := ""
		for _, row := range rows {
			if ruleName, exists := row.Values["rule_name"]; exists && ruleName != "" {
				sampleRuleName = ruleName
				break
			}
		}

		if sampleRuleName == "" {
			t.Skip("No rule names found in data")
		}

		filter := Eq("rule_name", sampleRuleName)
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"timestamp", "protocol", "action", "source_country", "destination_country"},
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		t.Logf("Rule name filter ('%s'): Found %d matches in %v", sampleRuleName, result.TotalMatches, result.QueryTime)
	})

	// Test 3: Complex AND query - protocol AND action
	t.Run("ProtocolAndActionFilter", func(t *testing.T) {
		filter := And(Eq("protocol", "TCP"), Eq("action", "Block"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"timestamp", "source_ip", "destination_ip", "rule_category", "rule_name"},
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		t.Logf("TCP AND Block filter: Found %d matches in %v", result.TotalMatches, result.QueryTime)

		// Verify results
		for i, row := range result.Rows {
			if i >= 3 { // Check first 3 rows
				break
			}
			t.Logf("Blocked TCP: %s -> %s [%s]", row["source_ip"], row["destination_ip"], row["rule_category"])
		}
	})

	// Test 4: Complex OR query - multiple protocols
	t.Run("MultipleProtocolsFilter", func(t *testing.T) {
		filter := Or(Eq("protocol", "TCP"), Eq("protocol", "UDP"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action", "source_country", "destination_country"},
			Limit:      10,
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		t.Logf("TCP OR UDP filter: Found %d total matches, returning %d rows in %v",
			result.TotalMatches, len(result.Rows), result.QueryTime)

		// Count protocols in results
		protocolCount := make(map[string]int)
		for _, row := range result.Rows {
			protocolCount[row["protocol"]]++
		}
		t.Logf("Protocol distribution: %v", protocolCount)
	})

	// Test 5: Complex nested query
	t.Run("ComplexNestedFilter", func(t *testing.T) {
		// (protocol=TCP AND action=Block) OR (protocol=UDP AND action=Allow)
		filter := Or(
			And(Eq("protocol", "TCP"), Eq("action", "Block")),
			And(Eq("protocol", "UDP"), Eq("action", "Allow")),
		)

		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action", "rule_category", "source_country", "destination_country"},
			Limit:      15,
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		t.Logf("Complex nested filter: Found %d total matches, returning %d rows in %v",
			result.TotalMatches, len(result.Rows), result.QueryTime)

		// Analyze results
		combinations := make(map[string]int)
		for _, row := range result.Rows {
			key := fmt.Sprintf("%s-%s", row["protocol"], row["action"])
			combinations[key]++
		}
		t.Logf("Protocol-Action combinations: %v", combinations)
	})

	// Test 6: Geographic filter
	t.Run("GeographicFilter", func(t *testing.T) {
		filter := Or(Eq("source_country", "China"), Eq("destination_country", "China"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"source_country", "destination_country", "protocol", "action"},
			Limit:      10,
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		t.Logf("China geographic filter: Found %d total matches in %v", result.TotalMatches, result.QueryTime)
	})

	// Test execution statistics
	t.Run("ExecutionStatistics", func(t *testing.T) {
		filter := And(Eq("protocol", "TCP"), Eq("rule_category", "Web Filtering"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"timestamp", "source_ip", "destination_ip", "action"},
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query execution failed: %v", err)
		}

		stats := result.Stats
		t.Logf("Execution Statistics:")
		t.Logf("  Indexes used: %v", stats.IndexesUsed)
		t.Logf("  Index lookups: %d", stats.IndexLookups)
		t.Logf("  Set operations: %d", stats.SetOperations)
		t.Logf("  Rows scanned: %d", stats.RowsScanned)
		t.Logf("  Query time: %v", result.QueryTime)

		if stats.IndexLookups != 2 {
			t.Errorf("Expected 2 index lookups, got %d", stats.IndexLookups)
		}
		if stats.SetOperations != 1 {
			t.Errorf("Expected 1 set operation, got %d", stats.SetOperations)
		}
	})
}

// BenchmarkMockDataQueries benchmarks various query patterns on mock_data.csv
func BenchmarkMockDataQueries(b *testing.B) {
	// Check if mock_data.csv exists
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		b.Skip("mock_data.csv not found, skipping benchmark")
	}

	// Setup (done once)
	tempDir, err := os.MkdirTemp("", "ultrafast_benchmark_*")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Read and index data
	rows, _, err := readFullCSV("mock_data.csv")
	if err != nil {
		b.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	generator := NewGenerator(tempDir)
	keyColumns := []string{"protocol", "rule_name", "action", "rule_category", "source_country"}

	for _, column := range keyColumns {
		columnData := extractColumnData(rows, column)
		if err := generator.Generate(column, columnData); err != nil {
			b.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
	}

	rowStore := NewRowStore(tempDir)
	if err := rowStore.GenerateRowStore("network_logs", rows); err != nil {
		b.Fatalf("Failed to generate row store: %v", err)
	}

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	b.Logf("Benchmark setup complete: %d rows indexed", len(rows))

	// Benchmark 1: Simple protocol filter
	b.Run("ProtocolFilter", func(b *testing.B) {
		filter := Eq("protocol", "TCP")
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"timestamp", "source_ip", "destination_ip", "action"},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result // Use result to prevent optimization
		}
	})

	// Benchmark 2: Complex AND query
	b.Run("ProtocolAndActionFilter", func(b *testing.B) {
		filter := And(Eq("protocol", "TCP"), Eq("action", "Block"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"timestamp", "source_ip", "destination_ip", "rule_category"},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result
		}
	})

	// Benchmark 3: Complex OR query
	b.Run("MultipleProtocolsFilter", func(b *testing.B) {
		filter := Or(Eq("protocol", "TCP"), Eq("protocol", "UDP"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action", "source_country"},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result
		}
	})

	// Benchmark 4: Complex nested query
	b.Run("ComplexNestedFilter", func(b *testing.B) {
		filter := Or(
			And(Eq("protocol", "TCP"), Eq("action", "Block")),
			And(Eq("protocol", "UDP"), Eq("action", "Allow")),
		)
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action", "rule_category", "source_country"},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result
		}
	})

	// Benchmark 5: Bitmap optimization test (large result set)
	b.Run("LargeResultSetBitmap", func(b *testing.B) {
		// Query that should return many results to trigger bitmap optimization
		filter := Or(
			Or(Eq("protocol", "TCP"), Eq("protocol", "UDP")),
			Eq("protocol", "ICMP"),
		)
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action"},
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result
		}
	})

	// Benchmark 6: Column projection performance
	b.Run("ColumnProjection", func(b *testing.B) {
		filter := Eq("protocol", "TCP")

		// Test with different numbers of projected columns
		queries := []*QueryRequest{
			{Filter: filter, SelectCols: []string{"timestamp"}},
			{Filter: filter, SelectCols: []string{"timestamp", "source_ip", "destination_ip"}},
			{Filter: filter, SelectCols: []string{"timestamp", "source_ip", "destination_ip", "action", "rule_name", "rule_category"}},
		}

		for _, query := range queries {
			b.Run(fmt.Sprintf("Cols%d", len(query.SelectCols)), func(b *testing.B) {
				b.ResetTimer()
				for j := 0; j < b.N; j++ {
					result, err := engine.ExecuteQuery("network_logs", query)
					if err != nil {
						b.Fatalf("Query failed: %v", err)
					}
					_ = result
				}
			})
		}
	})
}

// BenchmarkMockDataComparison compares regular vs bitmap execution
func BenchmarkMockDataComparison(b *testing.B) {
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		b.Skip("mock_data.csv not found, skipping benchmark")
	}

	// Setup
	tempDir, err := os.MkdirTemp("", "ultrafast_comparison_*")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	rows, _, err := readFullCSV("mock_data.csv")
	if err != nil {
		b.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	generator := NewGenerator(tempDir)
	keyColumns := []string{"protocol", "action", "rule_category"}

	for _, column := range keyColumns {
		columnData := extractColumnData(rows, column)
		if err := generator.Generate(column, columnData); err != nil {
			b.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
	}

	rowStore := NewRowStore(tempDir)
	if err := rowStore.GenerateRowStore("network_logs", rows); err != nil {
		b.Fatalf("Failed to generate row store: %v", err)
	}

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Query that should return many results
	filter := Or(Eq("protocol", "TCP"), Eq("protocol", "UDP"))
	query := &QueryRequest{
		Filter:     filter,
		SelectCols: []string{"protocol", "action", "rule_category"},
	}

	// Find max line number for bitmap optimization
	maxLineNumber := uint32(0)
	for _, row := range rows {
		if row.LineNumber > maxLineNumber {
			maxLineNumber = row.LineNumber
		}
	}

	b.Run("RegularExecution", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result
		}
	})

	b.Run("BitmapExecution", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQueryWithBitmaps("network_logs", query, maxLineNumber)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			_ = result
		}
	})
}
